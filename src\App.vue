<script setup lang="ts">
import { Toaster } from '@/components/ui/sonner'
import screenLayout from './layouts/index.vue'
import 'vue-sonner/style.css'
// import { onMounted } from 'vue'
// import autofit from 'autofit.js'

// onMounted(() => {
//   autofit.init({
//     dw: 2464,
//     dh: 1078,
//     el: 'body',
//     resize: true,
//   })
// })
</script>

<template>
  <Toaster
    :toastOptions="{
      // unstyled: true,
      classes: {
        error: 'p-6 bg-red-400/60  backdrop-blur-sm text-white text-sm font-medium rounded-lg border border-red-400',
        success: 'text-green-400',
        warning: 'text-yellow-400',
        info: 'bg-blue-400',
      },
    }"
  />
  <screen-layout>
    <router-view v-slot="{ Component }">
      <transition :enter-active-class="'animate-in fade-in'" :leave-active-class="'animate-out fade-out'" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </screen-layout>
</template>

<style>
.animate-in {
  animation-duration: 300ms;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-out {
  animation-duration: 300ms;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in {
  animation-name: fadeIn;
}

.fade-out {
  animation-name: fadeOut;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
</style>
