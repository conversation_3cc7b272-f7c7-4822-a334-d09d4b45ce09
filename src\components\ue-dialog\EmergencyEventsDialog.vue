<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

const props = defineProps<{
  open: boolean
  data?: {
    name: string
    time: string
    personInCharge: string
    address: string
    property: string
    phone: string
    status: string
    videos: {
      url: string
    }[]
  } | null
}>()
const emit = defineEmits<{
  (e: 'close'): void
}>()

const data = ref(
  props.data ?? {
    name: '燃气泄漏',
    time: '2025-07-02 15:25:50',
    personInCharge: '李**',
    phone: '19997631216',
    address: '容城镇容易线与顺达街交叉口',
    status: '已归档',
    videos: [
      { url: 'https://example.com/video1.mp4' },
      { url: 'https://example.com/video2.mp4' },
      { url: 'https://example.com/video3.mp4' },
    ],
  },
)

watch(
  () => props.data,
  newData => {
    if (newData) {
      data.value = newData
    }
  },
)

// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  // 当接收到 open 为 false 的更新时，触发 close 事件
  if (!open) {
    emit('close')
  }
}
</script>
<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[400px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">事件详情</DialogTitle>
        <DialogDescription as="div" class="p-6 text-sm text-white">
          <div class="flex">
            <div class="flex flex-col gap-5">
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">事件类型：</span>
                {{ data.name }}
              </p>
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">发生时间：</span>
                {{ data.time }}
              </p>
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">处置人：</span>
                {{ data.personInCharge }}
              </p>
            </div>
            <div class="flex flex-col gap-5 pt-6 text-white">
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">地址：</span>
                {{ data.address }}
              </p>
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">事件状态：</span>
                {{ data.status }}
              </p>
              <p class="flex">
                <span class="block w-[8em] text-right text-[#99D5FF]">区域监控：</span>
                刷新
              </p>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
      <div class="flex justify-between p-6">
        <div class="w-[245px] h-[184px]" v-for="video in data.videos" :key="video.url">
          <video
            class="object-fill w-full h-full"
            :src="video.url"
            disablepictureinpicture
            muted
            loop
            autoplay
            playsinline
          ></video>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
<style scoped>
.title-bg {
  background:
    url('@/assets/dialog/title-bg-400.png') no-repeat 0 0,
    linear-gradient(270deg, rgba(71, 235, 235, 0) 0%, rgba(71, 235, 235, 0) 60%, rgba(71, 235, 235, 0.3) 100%),
    linear-gradient(90deg, rgba(119, 168, 217, 0.3) 5%, rgba(105, 141, 191, 0.3) 80%, rgba(105, 141, 191, 0.3) 100%);
  background-size: cover;
  font-family: MStiffHei PRC;
}
</style>
