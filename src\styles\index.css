.panel-container {
  width: 744px;
  height: 304px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
.panel-container-half {
  width: 360px;
  height: 304px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-half.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
.panel-container-half-lr {
  width: 360px;
  height: 472px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-half-lr.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
  margin: 0;
}
.panel-container-col {
  width: 744;
  height: 468px;
  color: #fff;
  box-sizing: border-box;
  background: rgba(42, 56, 77, 0.6) url('@/assets/panel/section-bg-744x468.png') no-repeat 0 0;
  background-size: 100% 100%;
  backdrop-filter: blur(8px);
  border-radius: 8px;
}
/* 运输车辆页面：需要更矮的列容器（用于右侧列表或左侧指标卡伸展） */
.panel-container-col--short {
  height: 420px;
}
.panel-header {
  padding: 0 16px 0 52px;
  height: 48px;
  line-height: 44px; /* 注意：这里是44而不是48，不是错误 */
  text-align: left;
  font-size: 18px;

  /* .header-title {
    font-family: MStiffHei PRC;
  } */
}
.panel-content {
  width: 100%;
  height: calc(100% - 48px);
  box-sizing: border-box;
}

.layers-wrapper {
  position: relative;
  background: rgba(11, 46, 115, 0.6);
  box-sizing: border-box;
  backdrop-filter: blur(8px);
  border-width: 1px 0px 1px 0px;
  border-style: solid;
  border-image: linear-gradient(270deg, rgba(64, 159, 255, 0) 0%, #409fff 50%, rgba(64, 159, 255, 0) 100%) 1 0 1 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    border-width: 2px;
    border-style: solid;
    border-top-color: #66ffff;
    border-left-color: #66ffff;
    border-bottom-color: transparent;
    border-right-color: transparent;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-width: 2px;
    border-style: solid;
    border-top-color: #66ffff;
    border-right-color: #66ffff;
    border-bottom-color: transparent;
    border-left-color: transparent;
  }
}

.layers-wrapper-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px;

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 8px;
    height: 8px;
    border-width: 2px;
    border-style: solid;
    border-bottom-color: #66ffff;
    border-left-color: #66ffff;
    border-top-color: transparent;
    border-right-color: transparent;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 8px;
    height: 8px;
    border-width: 2px;
    border-style: solid;
    border-bottom-color: #66ffff;
    border-right-color: #66ffff;
    border-top-color: transparent;
    border-left-color: transparent;
  }
}
