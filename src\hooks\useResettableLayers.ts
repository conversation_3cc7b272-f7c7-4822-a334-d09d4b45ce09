import { ref, type Ref } from 'vue'
import type { Layers } from '@/types/mapLayers'

/**
 * Vue Composition API Hook，用于管理可重置的图层数据。
 * 它接收一个初始的图层数据对象，并提供一个响应式引用和重置函数。
 *
 * @param initialLayerData 页面特有的初始图层数据定义。
 * @returns 包含 layersData (Ref<Layers>) 和 resetLayersState (Function) 的对象。
 */
export function useResettableLayers(initialLayerData: Layers) {
  // 响应式图层数据，初始化为 initialLayerData 的一个深拷贝副本。
  // 确保 layersData.value 始终是一个独立的、可修改的对象，且保留初始的 checked 状态。
  const layersData: Ref<Layers> = ref(JSON.parse(JSON.stringify(initialLayerData)))

  /**
   * 重置 layersData 到其初始状态（即 initialLayerData 的深拷贝）。
   */
  const resetLayersState = () => {
    // 直接赋值 initialLayerData 的深拷贝，确保每次重置都是一个全新的独立对象。
    layersData.value = JSON.parse(JSON.stringify(initialLayerData))
  }

  return {
    layersData,
    resetLayersState,
  }
}
