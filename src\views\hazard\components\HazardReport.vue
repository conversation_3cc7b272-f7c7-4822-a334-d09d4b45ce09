<template>
  <div class="panel-container">
    <div class="panel-header">重点防控点位上报数据</div>
    <div class="p-4 panel-content">
      <div class="flex gap-6">
        <!-- 左侧3D环形图 -->
        <div class="chart-section">
          <div ref="chartRef" class="donut-chart"></div>
          <!-- 图例 -->
          <!-- <div class="legend-section">
            <div class="legend-item">
              <div class="legend-color" style="background-color: #55d0e0"></div>
              <span class="legend-text">2020-2022</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background-color: #f7b731"></div>
              <span class="legend-text">2022-2023</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background-color: #5a9fd4"></div>
              <span class="legend-text">2024-2025</span>
            </div>
          </div> -->
        </div>

        <!-- 右侧数据列表 -->
        <div class="data-list-section">
          <div v-for="(item, index) in dataList" :key="index">
            <div class="flex items-center justify-between">
              <div class="data-row-label">{{ item.label }}</div>
              <div class="data-row-value">{{ item.value }}</div>
            </div>
            <div class="progress-section">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: item.progress + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import { getPie3D } from '@/utils/echartsTools'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

// 右侧数据列表
const dataList = ref([
  {
    label: '这是对应内容',
    value: '33340',
    progress: 75,
  },
  {
    label: '这是对应内容',
    value: '33340',
    progress: 60,
  },
  {
    label: '这是对应内容',
    value: '33340',
    progress: 85,
  },
  {
    label: '这是对应内容',
    value: '33340',
    progress: 45,
  },
])

// 传入数据生成 option
const option = getPie3D(
  [
    {
      name: '2020-2022',
      value: 134,
      itemStyle: {
        color: '#55d0e0', // 青蓝色
      },
    },
    {
      name: '2022-2023',
      value: 56,
      itemStyle: {
        color: '#f7b731', // 黄色
      },
    },
    {
      name: '2024-2025',
      value: 57,
      itemStyle: {
        color: '#5a9fd4', // 蓝色
      },
    },
  ],
  0.5,
)

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initchart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>
<style scoped>
@import '@/styles/index.css';

.chart-section {
  width: 348px;
  height: 224px;
  display: flex;
  flex-direction: column;
}

.donut-chart {
  width: 100%;
  height: 100%;
}

.legend-section {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  font-size: 12px;
  color: #ffffff;
  font-family: Noto Sans SC;
}

.data-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-left: 20px;
}

.data-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
}

.data-row-label {
  font-size: 14px;
  color: #ffffff;
  font-family: Noto Sans SC;
  width: 120px;
}

.progress-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.progress-bar {
  flex: 1;
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.progress-fill {
  position: relative;
  height: 100%;
  background: linear-gradient(270deg, #ffc61a 0%, rgba(255, 198, 26, 0.45) 100%);
  transition: width 0.3s ease;

  &::after {
    content: '';
    position: absolute;
    top: -2px;
    right: 0;
    width: 4px;
    height: 16px;
    background-color: #ffc61a;
    z-index: 1;
  }
}

.data-row-value {
  font-size: 16px;
  font-weight: bold;
  color: #f7b731;
  font-family: Noto Sans SC;
  min-width: 60px;
  text-align: right;
}
</style>
