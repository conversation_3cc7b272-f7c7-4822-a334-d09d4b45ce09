<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <ComplaintOverview />
      <ChannelAnalysis />
      <TrendAnalysis />
    </div>

    <div class="center-panel-left">
      <LayersTool v-model="layersData" />
    </div>
    <!-- 右侧面板 -->
    <div class="right-panel">
      <RegionCompletionRanking />
      <TypeDistribution />
      <TaskSupervisionList />
    </div>
  </div>
</template>

<script setup lang="ts">
import ComplaintOverview from './components/ComplaintOverview.vue'
import ChannelAnalysis from './components/ChannelAnalysis.vue'
import TrendAnalysis from './components/TrendAnalysis.vue'
import RegionCompletionRanking from './components/RegionCompletionRanking.vue'
import TypeDistribution from './components/TypeDistribution.vue'
import TaskSupervisionList from './components/TaskSupervisionList.vue'
import { layerData } from './layerData'
import { ref } from 'vue'
import LayersTool from '@/components/LayersTool.vue'

const layersData = ref(layerData)
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 96px;
  left: 24px;
  right: 24px;
  height: calc(100% - 96px);
  overflow: hidden;
  z-index: 5;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 744px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.left-panel {
  left: 0;
}
.right-panel {
  right: 0;
}

.center-panel {
  position: absolute;
  top: 0;
  left: 768px;
  right: 768px;
  bottom: 0;
}

.center-panel-left {
  position: absolute;
  top: 0;
  left: 768px;
  z-index: 10;
}

.legend-wrapper {
  position: absolute;
  top: 12px;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: max-content;
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 6px 10px;
  background: rgba(11, 46, 115, 0.6);
  backdrop-filter: blur(8px);
  border-radius: 6px;
  border: 1px solid rgba(64, 159, 255, 0.6);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #66ffff;
  font-size: 12px;
}
.legend-icon {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
}
.legend-icon.supervision {
  background-image: url('/src/assets/map/supervision-matters-icon.png');
}
.legend-icon.heat {
  background-image: url('/src/assets/map/complaint-heat-icon.png');
}

@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
