<template>
  <div class="panel-container">
    <div class="panel-header">投诉类型分布</div>
    <div class="panel-content p-2">
      <div ref="container" class="w-full h-full"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { ref, onMounted, onBeforeUnmount } from 'vue'

const container = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null

const init = () => {
  if (!container.value) return
  chart = echarts.init(container.value)
  const data = [
    { name: '气价', value: 28.1 },
    { name: '安全', value: 21.4 },
    { name: '保供', value: 16.8 },
    { name: '服务', value: 18.7 },
    { name: '其他', value: 14.9 },
  ]
  chart.setOption({
    tooltip: { trigger: 'item' },
    legend: { right: 20, top: 'center', orient: 'vertical', textStyle: { color: '#66FFFF' } },
    series: [
      {
        type: 'pie',
        radius: ['45%', '70%'],
        center: ['40%', '50%'],
        label: { formatter: '{b}\n{d}%', color: '#AEE' },
        data,
      },
    ],
  })
}

onMounted(init)
onBeforeUnmount(() => chart?.dispose())
</script>

<style scoped>
@import '@/styles/index.css';
</style>
