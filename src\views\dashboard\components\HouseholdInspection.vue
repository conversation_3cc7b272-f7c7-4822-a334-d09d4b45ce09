<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">入户巡查</div>
      <div class="header-dropdown">
        <Select v-model:model-value="selectedOpt">
          <SelectTrigger class="text-sm dropdown-btn" size="sm">
            <SelectValue placeholder="统计周期" />
          </SelectTrigger>
          <SelectContent class="text-[#99D5FF]">
            <SelectGroup>
              <SelectItem value="2025">2025年</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="gauge-container">
        <div class="carousel">
          <div class="relative top-[-20px]" style="--i: 0">
            <div class="text-center">
              <span class="gauge-value">6</span>
              <span class="text-base">个</span>
            </div>
            <div class="gauge-label">巡检计划</div>
            <div class="gauge-circle-bg"></div>
          </div>
          <div class="relative top-[20px]" style="--i: 1">
            <div class="text-center">
              <span class="gauge-value">5</span>
              <span class="text-base">个</span>
            </div>
            <div class="gauge-label">完成计划</div>
            <div class="gauge-circle-bg"></div>
          </div>
          <div class="relative top-[-40px]" style="--i: 2">
            <div class="text-center">
              <span class="gauge-value">83.33</span>
              <span class="text-base">%</span>
            </div>
            <div class="gauge-label">巡检完成率</div>
            <div class="gauge-circle-bg"></div>
          </div>
          <div class="relative top-[20px]" style="--i: 3">
            <div class="text-center">
              <span class="gauge-value">10</span>
              <span class="text-base">个</span>
            </div>
            <div class="gauge-label">入户安检计划</div>
            <div class="gauge-circle-bg"></div>
          </div>
          <div class="relative top-[-20px]" style="--i: 4">
            <div class="text-center">
              <span class="gauge-value">14.51</span>
              <span class="text-base">万户</span>
            </div>
            <div class="gauge-label">安检用户</div>
            <div class="gauge-circle-bg"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

const selectedOpt = ref<string>('2025')
</script>

<style scoped>
@import '@/styles/index.css';

.header-title {
  color: white;
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  margin-left: 8px;
}

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.gauge-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  background: url('@/assets/household-inspection/indicator-con-bg.png') no-repeat center;
  background-size: cover;
  perspective: 1000px; /* 设置透视效果 */
}

.carousel {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d; /* 保持 3D 变换 */
  /* 旋转动画 */
  animation: rotate-carousel 20s infinite linear;
}

.carousel > div {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%) rotateY(calc(var(--i) * 72deg)) translateZ(260px); /* 定位到圆环上 */
}

.gauge-circle-bg {
  width: 72px;
  height: 60px;
  background: url('@/assets/household-inspection/indicator-bg.png') no-repeat center;
  background-size: cover;
}

@keyframes rotate-carousel {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(360deg);
  }
}

.gauge-value {
  color: #fff;
  font-family: DINPro;
  font-size: 24px;
  font-weight: bold;
  z-index: 1;
}

.gauge-label {
  color: #66ffff;
  font-size: 14px;
  text-align: center;
}
</style>
