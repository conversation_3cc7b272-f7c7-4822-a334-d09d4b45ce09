<template>
  <div class="panel-container">
    <div class="panel-header">
      <span class="header-title">地区达标情况</span>
      <div class="absolute z-10 flex justify-end w-full gap-2 top-3 right-4">
        <div
          class="w-10 h-6 text-center text-xs leading-[22px] border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'down' }"
          @click="activeTab = 'down'"
        >
          降序
        </div>
        <div
          class="w-10 h-6 text-center text-xs leading-[22px] border box-border border-[#99D5FF] bg-[#99D5FF]/15 cursor-pointer"
          :class="{ 'bg-[#FFD966]/15 border-[#FFD966]': activeTab === 'up' }"
          @click="activeTab = 'up'"
        >
          升序
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="region-list">
        <div v-for="(item, index) in regionData" :key="index" class="region-item">
          <div class="flex items-center justify-between">
            <div class="region-info">
              <div class="region-name">{{ item.name }}</div>
              <div class="region-detail">（共计{{ item.total }}家）</div>
            </div>
            <div class="progress-stats">
              <span class="percentage">{{ item.percentage }}%</span>
              <span class="compliance-count">达标{{ item.compliance }}家</span>
            </div>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: item.percentage + '%' }"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface RegionItem {
  name: string
  total: number
  compliance: number
  percentage: number
}
const activeTab = ref<'down' | 'up'>('down')

// 模拟地区数据
const regionData = ref<RegionItem[]>([
  {
    name: '区域1',
    total: 38,
    compliance: 36,
    percentage: 94.7,
  },
  {
    name: '区域1',
    total: 38,
    compliance: 36,
    percentage: 94.7,
  },
  {
    name: '区域1',
    total: 38,
    compliance: 36,
    percentage: 94.7,
  },
  {
    name: '区域1',
    total: 38,
    compliance: 36,
    percentage: 94.7,
  },
  {
    name: '区域1',
    total: 38,
    compliance: 36,
    percentage: 94.7,
  },
])
</script>

<style scoped>
@import '@/styles/index.css';

.region-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

.region-item {
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.region-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.region-name {
  font-size: 14px;
  color: #fff;
  font-family: 'Noto Sans SC', sans-serif;
  font-weight: 500;
}

.region-detail {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Noto Sans SC', sans-serif;
}

.progress-bar {
  padding: 1px;
  height: 12px;
  background: rgba(71, 235, 235, 0.15);
  border: 1px solid rgba(71, 235, 235, 0.3);
  border-radius: 12px;
  overflow: hidden;
  box-sizing: border-box;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #55d0e0 0%, #4ac3d3 100%);
  border-radius: 8px;
  transition: width 0.8s ease;
}

.progress-stats {
  display: flex;
  align-items: center;
  gap: 12px;
}

.percentage {
  font-size: 14px;
  font-weight: bold;
  color: #55d0e0;
  font-family: 'Noto Sans SC', sans-serif;
  min-width: 45px;
}

.compliance-count {
  font-size: 12px;
  color: #55d0e0;
  font-family: 'Noto Sans SC', sans-serif;
}
</style>
