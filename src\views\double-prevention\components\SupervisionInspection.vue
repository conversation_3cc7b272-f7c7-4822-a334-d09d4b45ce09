<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">风险增长趋势</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

let data = [
  {
    label: '周一',
    value: [
      { name: '高压', value: 6.8 },
      { name: '中低压', value: 7.4 },
      { name: '低压', value: 9.4 },
    ],
  },
  {
    label: '周二',
    value: [
      { name: '高压', value: 7 },
      { name: '中低压', value: 5.8 },
      { name: '低压', value: 9.5 },
    ],
  },
  {
    label: '周三',
    value: [
      { name: '高压', value: 9.2 },
      { name: '中低压', value: 6 },
      { name: '低压', value: 6.2 },
    ],
  },
  {
    label: '周四',
    value: [
      { name: '高压', value: 6.5 },
      { name: '中低压', value: 6.5 },
      { name: '低压', value: 8 },
    ],
  },
  {
    label: '周五',
    value: [
      { name: '高压', value: 8.3 },
      { name: '中低压', value: 5.3 },
      { name: '低压', value: 8 },
    ],
  },
  {
    label: '周六',
    value: [
      { name: '高压', value: 7.2 },
      { name: '中低压', value: 5.2 },
      { name: '低压', value: 8.2 },
    ],
  },
  {
    label: '周日',
    value: [
      { name: '高压', value: 8.8 },
      { name: '中低压', value: 5.8 },
      { name: '低压', value: 7.8 },
    ],
  },
]
let color = ['#FFF04C', '#FF4C4D', '#4BD9B5']

const option = {
  color,
  grid: {
    top: 40,
    left: 24,
    bottom: 30,
    right: 8,
  },
  legend: {
    top: 0,
    // right: 0,
    // icon: 'rect',
    // itemWidth: 10,
    // itemHeight: 10,
    // itemStyle: {
    //   borderWidth: 0,
    // },
    inactiveBorderWidth: 0,
    textStyle: {
      color: '#fff',
    },
  },
  tooltip: {
    show: true,
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        color: '#FFF04C',
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
    formatter: (p: any) => {
      if (p[0].dataIndex === 0 || p[0].dataIndex === data.length + 1) {
        return ''
      } else {
        return (
          p[0].axisValue +
          p
            .map(
              (i: any) =>
                `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${i.name}: ${i.value}</div>`,
            )
            .join('')
        )
      }
    },
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#fff',
        type: 'solid',
        opacity: 0.3,
      },
    },
    boundaryGap: false,
    data: ['', ...data.map(i => i.label), ''],
    axisLabel: {
      color: '#fff', // 横坐标文字为白色
    },
  },
  yAxis: {
    type: 'value',
    name: '单位：Mpa',
    nameTextStyle: {
      color: '#fff',
      align: 'left',
    },
    splitLine: {
      lineStyle: {
        color: '#fff',
        opacity: 0.3,
        type: 'dashed',
      },
    },
    axisLabel: {
      color: '#fff',
    },
  },
  series: data[0].value.map((item, index) => ({
    name: item.name,
    type: 'line',
    smooth: 0.5,
    symbol: 'none', // 取消点
    symbolSize: 8,
    lineStyle: {
      color: color[index],
      width: 1, // 线条变细
    },
    itemStyle: {
      color: (p: any) => {
        if (p.dataIndex !== 0 && p.dataIndex !== data.length + 1) return color[index]
      },
      borderColor: '#fff',
      borderWidth: 1,
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0, color: color[index] + '4D' },
          { offset: 1, color: color[index] + '00' },
        ],
      },
    },
    data: [
      data.reduce((a, b) => a + b.value[index].value, 0) / data.length,
      ...data.map(i => i.value[index]),
      data.reduce((a, b) => a + b.value[index].value, 0) / data.length,
    ],
  })),
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = option.xAxis.data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
