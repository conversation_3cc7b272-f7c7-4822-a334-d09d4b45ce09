<template>
  <div class="panel-container">
    <div class="panel-header">入户安检总览</div>
    <div class="industry-grid">
      <div v-for="item in industryData" :key="item.id" class="industry-card">
        <!-- 数值区域 -->
        <div class="card-value-section" v-if="item.id !== 3">
          <div class="value-number">{{ item.value }}</div>
          <div v-if="item.unit" class="value-unit">{{ item.unit }}</div>
        </div>

        <!-- 图效果 -->
        <div class="cylinder-container">
          <img v-if="item.id !== 3" src="/src/assets/indoor-icon/aj-icon.svg" alt="" />
          <div v-else class="ring-container">
            <div class="ring-wrapper"></div>
            <div class="ring-center-text">
              <div class="value-number">{{ item.value }}</div>
              <div class="value-unit">{{ item.unit }}</div>
            </div>
          </div>
        </div>

        <!-- 标题 -->
        <div class="card-title">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const industryData = ref([
  {
    id: 1,
    name: '年度应检用户',
    value: 1374,
    unit: '',
    icon: '/src/assets/indoor-icon/aj-icon.svg',
  },
  {
    id: 2,
    name: '年度应检用户',
    value: 1374,
    unit: '',
    icon: '/src/assets/indoor-icon/aj-icon.svg',
  },
  {
    id: 3,
    name: '年度安检率',
    value: 85,
    unit: '%',
    icon: '/src/assets/indoor-icon/aj-icon.svg',
  },
])
</script>
<style lang="scss" scoped>
@import '@/styles/index.css';

.industry-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  padding: 30px 20px 20px 20px;
}

.industry-card {
  width: 100%;
  max-width: 200px;
  height: 200px;
  background: transparent;
  border: none;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 20px 0;
  overflow: visible;
}

.card-value-section {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 5px;
  z-index: 10;

  .value-number {
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 24px;
    line-height: 1;
    text-align: center;
    font-weight: bold;
    margin-right: 2px;
    margin-bottom: -60px;
  }

  .value-unit {
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 16px;
    line-height: 1;
    text-align: center;
  }
}

.cylinder-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 5px 0;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 120px;
    height: 120px;
  }

  .ring-container {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .ring-wrapper {
    width: 120px;
    height: 120px;
    background: url('/src/assets/indoor-icon/greenRing.svg') no-repeat;
    background-size: 100% 100%;
    position: relative;
  }

  .ring-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    animation: rotate 2s linear infinite;
    transform-origin: center center;
  }

  .ring-center-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: row;
    align-items: baseline;
    justify-content: center;
    z-index: 10;

    .value-number {
      color: #ffffff;
      font-family: 'Noto Sans SC', sans-serif;
      font-size: 24px;
      line-height: 1;
      text-align: center;
      font-weight: bold;
      margin-right: 2px;
    }

    .value-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC', sans-serif;
      font-size: 16px;
      line-height: 1;
      text-align: center;
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

.card-title {
  color: #ffffff;
  font-family: 'Noto Sans SC', sans-serif;
  font-size: 14px;
  line-height: 1.2;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  margin-top: auto;
  margin-bottom: 0;
}
</style>
