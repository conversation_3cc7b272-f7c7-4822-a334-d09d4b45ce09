<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">入户安检率分析</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'administrative' }"
            @click="selectedOpt = 'administrative'"
          >
            行政区划
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'enterprise' }"
            @click="selectedOpt = 'enterprise'"
          >
            燃气企业
          </button>
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
const selectedOpt = ref<string>('administrative')
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

let xAxis = ['07/28', '07/29', '07/30', '07/31', '08/01', '08/02', '08/03']
let yData = [120, 200, 150, 80, 70, 110, 130]
let lineData = [65, 78, 85, 72, 58, 68, 70]

const option = {
  backgroundColor: 'transparent',
  grid: {
    left: '5%',
    right: '5%',
    top: '20%',
    bottom: '5%',
    containLabel: true,
  },
  legend: {
    data: ['总配数', '安全率'],
    textStyle: {
      color: '#fff',
      fontSize: 12,
    },
    itemWidth: 15,
    itemHeight: 10,
    icon: 'rect',
    itemGap: 20,
    top: '2%',
    right: '40%',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    textStyle: {
      color: '#fff',
    },
    backgroundColor: 'rgba(42, 56, 77, 0.6)',
    borderWidth: 0,
  },
  xAxis: [
    {
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#fff',
          type: 'solid',
          opacity: 0.3,
        },
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12,
      },
      data: xAxis,
    },
    {
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      data: xAxis,
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '单位：个',
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 1)',
        fontSize: 12,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 1)',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    {
      type: 'value',
      name: '单位：%',
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 1)',
        fontSize: 12,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 1)',
        fontSize: 12,
        formatter: '{value}%',
      },
      splitLine: {
        show: false,
      },
      max: 100,
      min: 0,
    },
  ],
  series: [
    {
      name: '总配数',
      type: 'bar',
      barWidth: 20,
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(71, 235, 235, 0.8)' },
            { offset: 1, color: 'rgba(71, 235, 235, 0)' },
          ],
        },
        borderColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(71, 235, 235, 0.8)' },
            { offset: 1, color: 'rgba(71, 235, 235, 0.2)' },
          ],
        },
        borderWidth: 1,
      },
      data: yData,
    },
    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'rect',
      symbolRotate: 45,
      symbolPosition: 'end',
      symbolSize: [7, 7],
      symbolOffset: [0, '-50%'],
      z: 10,
      itemStyle: {
        color: '#47EBEB',
      },
      tooltip: { show: false },
      data: yData,
    },
    {
      type: 'bar',
      xAxisIndex: 1,
      barWidth: 1,
      itemStyle: {
        color: 'transparent',
        borderColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(71, 235, 235, 0.65)' },
            { offset: 1, color: 'rgba(71, 235, 235, 0.15)' },
          ],
        },
        borderWidth: 1,
        borderType: [1, 3],
      },
      tooltip: { show: false },
      data: yData,
    },
    {
      name: '安全率',

      type: 'line',
      yAxisIndex: 1,
      smooth: 0.4,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        color: '#FFD700',
        width: 0.5,
      },
      itemStyle: {
        color: '#FFD700',
        borderColor: '#fff',
        borderWidth: 2,
      },
      data: lineData,
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = option.xAxis[0].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}

.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
