# 燃气大屏系统

## 项目概述

本项目是一个基于Vue 3 + TypeScript + ECharts + AMap的燃气安全监控大屏系统，用于展示城市燃气安全相关的各项数据和指标。

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: shadcn-vue
- **样式框架**: Tailwind CSS
- **图表库**: ECharts 6 + AMap
- **图标库**: Lucide Vue Next
- **构建工具**: Vite
- **包管理器**: pnpm

## 目录结构

```
├── public
│   ├── favicon.ico
│   └── mock
├── src
│   ├── assets
│   │   ├── footer
│   │   │   ├── scroll-icon.png
│   │   │   └── scroll-icon-active.png
│   │   ├── header
│   │   │   ├── logo.png
│   │   │   └── <EMAIL>
│   │   └── images
│   │       ├── bg.png
│   │       ├── <EMAIL>
│   │       ├── <EMAIL>
│   │       ├── <EMAIL>
│   │       └── <EMAIL>
│   ├── components
│   │   ├── ui
│   │   │   ├── button
│   │   │   │   ├── index.ts
│   │   │   │   └── index.vue
│   │   │   ├── carousel
│   │   │   │   ├── index.ts
│   │   │   │   ├── interface.ts
│   │   │   │   ├── useCarousel.ts
│   │   │   │   └── index.vue
│   │   │   └── index.ts
│   │   ├── LayersTool.vue
│   │   └── map.vue
│   ├── layouts
│   │   ├── components
│   │   │   ├── Warning.vue
│   │   │   ├── footer.vue
│   │   │   └── header.vue
│   │   └── index.vue
│   ├── lib
│   │   └── utils.ts
│   ├── App.vue
│   ├── main.ts
│   ├── style.css
│   ├── types
│   ├── router
│   │   ├── index.ts
│   │   └── routes.ts
│   ├── store
│   │   ├── index.ts
│   │   └── modules
│   │       ├── app.ts
│   │       ├── index.ts
│   │       └── user.ts
│   ├── styles
│   │   ├── index.scss
│   │   └── variables.scss
│   ├── utils
│   │   ├── index.ts
│   │   └── useWindowResize.ts
│   └── views
│       ├── home.vue
│       ├── index.vue
│       └── not-found.vue
├── .env
├── .env.development
├── .env.production
├── .gitignore
├── .npmrc
├── .prettierrc
├── .prettierignore
├── components.json
├── Dockerfile
├── index.html
├── nginx.conf
├── package.json
├── pnpm-lock.yaml
├── postcss.config.mjs
├── README.md
├── tsconfig.json
├── tsconfig.node.json
├── vite.config.ts
└── tailwind.config.js
```

## 开发指南

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
pnpm dev
```

### 构建生产版本

```bash
pnpm build
```

### 预览生产版本

```bash
pnpm preview
```
