<template>
  <div class="panel-container">
    <div class="panel-header">投诉事件总览</div>
    <div class="panel-content p-4">
      <div class="grid grid-cols-3 gap-4">
        <div v-for="item in items" :key="item.key" class="flex items-center gap-3 p-3 bg-white/0 rounded">
          <img :src="item.icon" alt="" class="w-10 h-10" />
          <div class="flex-1">
            <div class="text-sm text-white/80">{{ item.label }}</div>
            <div class="flex items-end gap-2">
              <div class="text-2xl text-[#66FFFF] font-semibold">{{ item.value }}</div>
              <div class="text-xs text-white/60">{{ item.sub }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import complaintsIcon from '@/assets/complaint/complaints-num.png'
import acceptanceIcon from '@/assets/complaint/acceptance-num.png'
import completedIcon from '@/assets/complaint/completed-num.png'
import transferIcon from '@/assets/complaint/transfer-num.png'
import returnVisitIcon from '@/assets/complaint/return-visit-num.png'
import archiveIcon from '@/assets/complaint/archive-num.png'

const items = [
  { key: 'complaints', label: '投诉量', value: 6248, sub: '本月新增 432', icon: complaintsIcon },
  { key: 'acceptance', label: '受理量', value: 6248, sub: '受理率 87.4%', icon: acceptanceIcon },
  { key: 'completed', label: '办结量', value: 6248, sub: '办结率 87.4%', icon: completedIcon },
  { key: 'transfer', label: '转办量', value: 6248, sub: '', icon: transferIcon },
  { key: 'return', label: '回访量', value: 6248, sub: '', icon: returnVisitIcon },
  { key: 'archive', label: '归档量', value: 6248, sub: '', icon: archiveIcon },
]
</script>

<style scoped>
@import '@/styles/index.css';
</style>
