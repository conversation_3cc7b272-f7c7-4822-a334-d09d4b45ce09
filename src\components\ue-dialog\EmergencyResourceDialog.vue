<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

const props = defineProps<{
  open: boolean
  data?: {
    name: string
    personInCharge: string
    address: string
    property: string
    phone: string
  } | null
}>()
const emit = defineEmits<{
  (e: 'close'): void
}>()

const data = ref(
  props.data ?? {
    name: '容城中石油昆仑应急资源库',
    property: '物资仓库',
    personInCharge: '张*',
    phone: '19997631216',
    address: '容城县/豪丹路与建兴街交叉口西',
  },
)

watch(
  () => props.data,
  newData => {
    if (newData) {
      data.value = newData
    }
  },
)

// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  // 当接收到 open 为 false 的更新时，触发 close 事件
  if (!open) {
    emit('close')
  }
}
</script>
<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[400px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">资源详情</DialogTitle>
        <DialogDescription as="div" class="p-6 text-sm text-white">
          <div class="flex flex-col gap-5">
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">资源名称：</span>
              {{ data.name }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">资源性质：</span>
              {{ data.property }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">负责人：</span>
              {{ data.personInCharge }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">负责人电话：</span>
              {{ data.phone }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">地址：</span>
              {{ data.address }}
            </p>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>
<style scoped>
.title-bg {
  background:
    url('@/assets/dialog/title-bg-400.png') no-repeat 0 0,
    linear-gradient(270deg, rgba(71, 235, 235, 0) 0%, rgba(71, 235, 235, 0) 60%, rgba(71, 235, 235, 0.3) 100%),
    linear-gradient(90deg, rgba(119, 168, 217, 0.3) 5%, rgba(105, 141, 191, 0.3) 80%, rgba(105, 141, 191, 0.3) 100%);
  background-size: cover;
  font-family: MStiffHei PRC;
}
</style>
