<template>
  <div class="panel-container-col">
    <div class="panel-header">用户设备告警</div>
    <div class="p-4 panel-content">
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 33%" />
          <col style="width: 33%" />
          <col style="width: 24%" />
          <col style="width: 10%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">设备编号</TableHead>
            <TableHead class="font-bold text-white">告警时间</TableHead>
            <TableHead class="font-bold text-white">告警原因</TableHead>
            <TableHead class="font-bold text-white">操作</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${7 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 33%" />
            <col style="width: 33%" />
            <col style="width: 24%" />
            <col style="width: 10%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.id }}</TableCell>
              <TableCell>{{ item.time }}</TableCell>
              <TableCell>{{ item.remark }}</TableCell>
              <TableCell class="text-[#99D5FF] cursor-pointer" @click="handleShowDialog(item)">查看</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Table,
  TableBody,
  // TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

const handleShowDialog = (record: any) => {
  emit('click', record)
}

const sourceData = ref([
  {
    id: '***************',
    time: '2025-07-07 19:40:14',
    remark: '浓度超标告警',
    paymentMethod: 'Credit Card',
  },
  {
    id: '***************',
    time: '2025-06-20 18:35:10',
    remark: '浓度超标告警',
    paymentMethod: 'PayPal',
  },
  {
    id: '***************',
    time: '2025-06-17 17:30:05',
    remark: '浓度超标告警',
    paymentMethod: 'Bank Transfer',
  },
  {
    id: '***************',
    time: '2025-06-01 16:25:00',
    remark: '浓度超标告警',
    paymentMethod: 'Credit Card',
  },
  {
    id: '***************',
    time: '2025-05-28 15:20:55',
    remark: '浓度超标告警',
    paymentMethod: 'PayPal',
  },
  {
    id: '***************',
    time: '2025-05-15 14:15:50',
    remark: '浓度超标告警',
    paymentMethod: 'Bank Transfer',
  },
  {
    id: '***************',
    time: '2025-05-08 13:10:45',
    remark: '浓度超标告警',
    paymentMethod: 'Credit Card',
  },
  {
    id: '***************',
    time: '2025-04-24 12:05:40',
    remark: '浓度超标告警',
    paymentMethod: 'PayPal',
  },
  {
    id: '***************',
    time: '2025-04-10 11:00:35',
    remark: '浓度超标告警',
    paymentMethod: 'Bank Transfer',
  },
])

const scrollList = computed(() => [...sourceData.value, ...sourceData.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 48 // 每行高度为40px
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight

    if (scrollTop.value >= sourceData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>
<style scoped>
@import '@/styles/index.css';
</style>
