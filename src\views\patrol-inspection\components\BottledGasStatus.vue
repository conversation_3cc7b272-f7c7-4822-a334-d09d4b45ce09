<template>
  <div class="panel-container-half">
    <div class="panel-header">
      <div class="header-title">瓶装气状态</div>
    </div>
    <div class="p-4 panel-content">
      <div class="status-indicators">
        <div class="text-center">
          <div class="status-value">20360个</div>
          <div class="status-icon green animate-pulse"></div>
          <div class="status-label">在用</div>
        </div>
        <div class="text-center">
          <div class="status-value">1756个</div>
          <div class="status-icon yellow animate-pulse"></div>
          <div class="status-label">停用</div>
        </div>
        <div class="text-center">
          <div class="status-value">861个</div>
          <div class="status-icon red animate-pulse"></div>
          <div class="status-label">废弃</div>
        </div>
      </div>
      <div class="chart-section">
        <div ref="chartRef" class="donut-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

const option = {
  backgroundColor: 'transparent',
  legend: {
    show: true,
    orient: 'vertical',
    right: '0%',
    bottom: '0%',
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 12,
    itemHeight: 8,
    icon: 'rect',
  },
  series: [
    {
      type: 'pie',
      radius: ['51.8%', '71.4%'],
      center: ['42.86%', '50%'],
      silent: true,
      data: [{ value: 1, itemStyle: { color: 'rgba(153, 213, 255, 0.15)' } }],
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
    },
    {
      type: 'pie',
      radius: ['57.1%', '66.1%'],
      center: ['42.86%', '50%'],
      data: [
        { value: 8, name: '在用', itemStyle: { color: '#409FFF' } },
        { value: 7, name: '停用', itemStyle: { color: '#C0FFB3' } },
        { value: 5, name: '废弃', itemStyle: { color: '#FF791A' } },
      ],
      label: {
        show: true,
        color: '#fff',
        formatter: `{percent|{d}%}\n{value|{c}}条`,
        rich: {
          percent: {
            fontSize: 20,
            color: '#fff',
          },
          value: {
            fontSize: 12,
            color: '#fff',
          },
        },
      },
      labelLine: {
        show: true,
      },
    },
  ],
}
const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startHighlightAnimation()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = option.series[1].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 1, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 1, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-indicators {
  width: 100%;
  height: 48px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-around;
  gap: 16px;
}

.status-icon {
  width: 70px;
  height: 36px;
  background-size: 48px;
}

.status-icon.green {
  background: url('@/assets/bottled-gas-status/online.svg') no-repeat center bottom;
}

.status-icon.yellow {
  background: url('@/assets/bottled-gas-status/offline.svg') no-repeat center bottom;
}

.status-icon.red {
  background: url('@/assets/bottled-gas-status/drop.svg') no-repeat center bottom;
}

.status-value {
  font-family: NotoSansSC;
  font-size: 18px;
  font-weight: normal;
  line-height: 26px;
  letter-spacing: normal;
  color: #fff;
  white-space: nowrap;
  position: relative;
  top: 16px;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: normal;
  color: #ffffff;
}

.chart-section {
  width: 448px;
  height: 168px;
}

.donut-chart {
  width: 100%;
  height: 100%;
}
</style>
