<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">企业用户技防设施接入统计</div>
      <div class="header-dropdown">
        <Select v-bind:model-value="selectedOpt">
          <SelectTrigger class="text-sm dropdown-btn" size="sm">
            <SelectValue placeholder="统计企业涉气" />
          </SelectTrigger>
          <SelectContent class="text-[#99D5FF]">
            <SelectGroup>
              <!-- <SelectLabel>Fruits</SelectLabel> -->
              <SelectItem value="week">企业1</SelectItem>
              <SelectItem value="month">企业2</SelectItem>
              <SelectItem value="year">企业3</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
    <div class="panel-content">
      <div class="content-layout">
        <!-- 左侧统计卡片区域 -->
        <div class="stats-section">
          <!-- 已接入用户卡片 -->
          <div class="frame">
            <div class="group-1000007074">
              <img class="union" src="@/assets/industry-icons/box-ImgBlue.svg" alt="蓝色装饰" />
              <div class="rectangle-96076530"></div>
            </div>
            <div class="text">已接入用户</div>
            <div class="frame-1">
              <div class="text-806">{{ accessedUsers }}</div>
              <div class="text-1">占比：{{ accessedRatio }}%</div>
            </div>
          </div>

          <!-- 未接入用户卡片 -->
          <div class="frame">
            <div class="group-1000007074">
              <img class="union" src="@/assets/industry-icons/box-ImgYel.svg" alt="黄色装饰" />
            </div>
            <div class="text">未接入用户</div>
            <div class="frame-1">
              <div class="text-201">{{ notAccessedUsers }}</div>
              <div class="text-1">占比：{{ notAccessedRatio }}%</div>
            </div>
          </div>
        </div>

        <!-- 右侧图表区域 -->
        <div class="chart-section">
          <div ref="chartContainer" class="chart-container"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

// 响应式数据
const accessedUsers = ref(806)
const accessedRatio = ref(80)
const notAccessedUsers = ref(201)
const notAccessedRatio = ref(20)
const selectedOpt = ref<string>('week')

// 图表相关
const chartContainer = ref<HTMLElement>()
let chart: ECharts | null = null

const initChart = () => {
  if (!chartContainer.value) return

  chart = echarts.init(chartContainer.value)

  // 使用5条数据
  let yValue = [
    [10, 12, 15, 26, 5],
    [0.5, 1.2, 2.8, 1.6, 1],
  ]
  let names = ['已接入用户', '未接入用户']
  let xValue = ['国家部委', '地方政府', '科研院所', '高等院校', '其他']
  let colorList = [
    { color0: 'rgba(102, 255, 255, 1)', color1: 'rgba(102, 255, 255, 0.45)' },
    { color0: 'rgba(255, 198, 26, 1)', color1: 'rgba(255, 198, 26, 0.45)' },
  ]
  let series: any[] = []
  let xoffset = (names.length - 1) * -8.6
  names.forEach((item, index) => {
    series.push({
      name: item,
      type: 'pictorialBar',
      symbol: 'rect',
      symbolSize: [2.5, 13],
      symbolPosition: 'end',
      symbolOffset: [2, xoffset + index * 17.4],
      z: 2,
      tooltip: {
        show: false,
      },
      itemStyle: {
        color: colorList[index].color0,
      },
      data: yValue[index],
      xAxisIndex: index,
    })
    series.push({
      name: item,
      type: 'bar',
      barGap: '80%',
      barWidth: 10,
      z: 1,
      label: {
        show: true,
        position: 'right',
        color: colorList[index].color1,
        offset: [10, 0],
      },
      itemStyle: {
        color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
          {
            offset: 0,
            color: colorList[index].color0,
          },
          {
            offset: 1,
            color: colorList[index].color1,
          },
        ]),
      },
      data: yValue[index],
      xAxisIndex: index,
    })
  })
  let option = {
    backgroundColor: 'transparent', // 背景透明
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none',
      },
      formatter: function (prams: any) {
        var newStr = prams[0].name + ':<br/>'
        for (let index in prams) {
          let unit = prams[index].seriesName == '已接入用户' ? '人' : '人'
          newStr += prams[index].marker + ' ' + prams[index].seriesName + '：' + prams[index].value + unit + '<br/>'
        }
        return newStr
      },
    },
    grid: {
      left: '0%',
      right: '15%',
      bottom: '0%',
      top: '8%',
      containLabel: true,
    },
    yAxis: [
      {
        type: 'category',
        axisLabel: {
          interval: 0, // 解决x轴名称过长问题
          fontSize: 12,
          color: '#99D5FF', // 改为主题色
          lineHeight: 16,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
          lineStyle: {
            type: 'solid',
            color: 'rgba(64, 159, 255, 0.3)', // 改为主题色
          },
        },
        data: xValue,
      },
    ],
    xAxis: [
      {
        name: '', //名称隐藏
        min: 0,
        max: Math.max(...yValue[0]),
        type: 'value', //连续类型
        nameTextStyle: {
          padding: [0, 0, 0, 0], // 修改单位位置
          color: '#99D5FF',
          fontSize: 14,
          fontWeight: 400,
        },
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#99D5FF',
          fontSize: 14,
          fontWeight: 400,
        },
      },
      {
        name: '', //名称隐藏
        min: 0,
        max: Math.max(...yValue[1]),
        type: 'value', //连续类型
        nameTextStyle: {
          padding: [0, 0, 0, 5], // 修改单位位置
          verticalAlign: 'bottom',
          color: '#99D5FF',
          fontSize: 14,
          fontWeight: 400,
        },
        axisLine: {
          //坐标轴样式
          show: false, //不显示
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false, // 隐藏上方刻度标签
        },
      },
    ],
    series: series,
  }

  chart.setOption(option)
}
// 监听窗口大小变化
const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()

  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.panel-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.panel-content {
  padding: 16px;
  height: calc(100% - 55px);
  background: rgba(0, 0, 0, 0.02);
}

.content-layout {
  display: flex;
  height: 100%;
  gap: 12px;
}

.stats-section {
  flex: 0 0 180px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: center;
}

.frame {
  width: 160px;
  height: 108px;
  background: linear-gradient(0deg, rgba(64, 159, 255, 0.15) 0%, rgba(64, 159, 255, 0) 100%);
  border-left: 1px solid rgba(64, 159, 255, 0.15);
  border-right: 1px solid rgba(64, 159, 255, 0.15);
  border-bottom: 1px solid rgba(64, 159, 255, 0.15);
  position: relative;

  .group-1000007074 {
    position: absolute;
    top: 34px;
    left: calc(100% - 160px + 40px);
    width: 80px;
    height: 46px;

    .union {
      position: absolute;
      top: -1px;
      left: calc(100% - 80px + -1px);
      width: 80px;
      height: 42px;
    }
  }

  .text {
    position: absolute;
    left: calc(100% - 160px + 45px);
    bottom: 4px;
    width: 70px;
    height: 20px;
    white-space: nowrap;
    color: #99d5ff;
    font-family: 'Noto Sans SC';
    font-size: 13px;
    line-height: 20px;
    text-align: center;
  }

  .frame-1 {
    position: absolute;
    top: 8px;
    left: calc(100% - 160px + 49px);
    width: 61px;
    height: 48px;
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: center;

    .text-806 {
      flex-shrink: 0;
      width: 40px;
      height: 32px;
      white-space: nowrap;
      color: #66ffff;
      font-family: 'DINPro';
      font-size: 24px;
      line-height: 32px;
      text-align: center;
    }

    .text-201 {
      flex-shrink: 0;
      width: 40px;
      height: 32px;
      white-space: nowrap;
      color: #ffc61a;
      font-family: 'DINPro';
      font-size: 24px;
      line-height: 32px;
      text-align: center;
    }
    .text-1 {
      flex-shrink: 0;
      width: 61px;
      height: 16px;
      white-space: nowrap;
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 12px;
      line-height: 16px;
      text-align: center;
    }
  }
}

.chart-section {
  flex: 1;
  background: transparent;
  padding: 8px;
}

.chart-container {
  margin-top: -30px;
  width: 100%;
  height: 100%;
  min-height: 260px;
}

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.header-dropdown {
  display: flex;
  align-items: center;
}
</style>
