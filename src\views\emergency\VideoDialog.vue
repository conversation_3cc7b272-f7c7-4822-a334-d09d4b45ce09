<script setup lang="ts">
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogHeader,
  DialogTitle,
  // DialogClose,
} from '@/components/ui/dialog'

const props = defineProps<{
  open: boolean
  data: {
    id: string
    title: string
    address: string
    url: string
  }
}>()
const emit = defineEmits<{
  (e: 'close'): void
}>()

// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  // 当接收到 open 为 false 的更新时，触发 close 事件
  if (!open) {
    emit('close')
  }
}
</script>
<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[800px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">周边监控</DialogTitle>
        <DialogDescription>
          <div class="flex flex-col gap-5 pt-6 text-white">
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">监控名称：</span>
              {{ props.data.title }}
            </p>
            <p class="flex">
              <span class="block w-[8em] text-right text-[#99D5FF]">地址：</span>
              {{ props.data.address }}
            </p>
          </div>
        </DialogDescription>
      </DialogHeader>
      <div class="p-6">
        <video
          class="object-contain w-full h-full"
          :src="props.data.url"
          disablepictureinpicture
          muted
          loop
          autoplay
          playsinline
        ></video>
      </div>
    </DialogContent>
  </Dialog>
</template>
<style scoped>
.title-bg {
  background:
    url('@/assets/dialog/title-bg-800.png') no-repeat center,
    linear-gradient(270deg, rgba(71, 235, 235, 0) 0%, rgba(71, 235, 235, 0) 60%, rgba(71, 235, 235, 0.3) 100%),
    linear-gradient(90deg, rgba(119, 168, 217, 0.3) 5%, rgba(105, 141, 191, 0.3) 80%, rgba(105, 141, 191, 0.3) 100%);
  background-size: cover;
  font-family: MStiffHei PRC;
}
</style>
