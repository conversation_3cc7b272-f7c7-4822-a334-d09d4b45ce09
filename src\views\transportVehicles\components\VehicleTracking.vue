<template>
  <div class="panel-container-col" style="margin: 0">
    <div class="panel-header">车辆定位追踪</div>
    <div class="p-4 panel-content">
      <div class="filter-bar">
        <div class="input-with-icon">
          <Search class="icon" />
          <input v-model.trim="keyword" class="filter-input" type="text" placeholder="搜索所属服务企业" />
        </div>
      </div>
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 22%" />
          <col style="width: 18%" />
          <col style="width: 18%" />
          <col style="width: auto" />
          <col style="width: 12%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">所属区域</TableHead>
            <TableHead class="font-bold text-white">车辆型号</TableHead>
            <TableHead class="font-bold text-white">车辆号码</TableHead>
            <TableHead class="font-bold text-white">所属服务企业</TableHead>
            <TableHead class="font-bold text-white">操作</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${6 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 22%" />
            <col style="width: 18%" />
            <col style="width: 18%" />
            <col style="width: auto" />
            <col style="width: 12%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.region }}</TableCell>
              <TableCell>{{ item.plate }}</TableCell>
              <TableCell>{{ item.vin }}</TableCell>
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.company }}</TableCell>
              <TableCell class="action">查看</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Search } from 'lucide-vue-next'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

const allRows = ref(
  Array.from({ length: 12 }).map((_, i) => ({
    region: '这是对应内容信息',
    plate: '冀D·A1234',
    vin: 'VIN000' + (i + 1).toString().padStart(3, '0'),
    company: i % 2 === 0 ? '肥乡燃气集团' : '城市运营公司',
  })),
)

const keyword = ref('')
const rows = computed(() => {
  const kw = keyword.value.trim()
  if (!kw) return allRows.value
  return allRows.value.filter(r => r.company.includes(kw))
})
const scrollList = computed(() => [...rows.value, ...rows.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 48
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight
    if (scrollTop.value >= rows.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';
.filter-bar {
  margin-bottom: 12px;
}
.input-with-icon {
  position: relative;
  width: 100%;
}
.icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: #99d5ff;
  opacity: 0.9;
  pointer-events: none;
}
.filter-input {
  width: 100%;
  height: 32px;
  padding: 0 12px 0 36px;
  color: #e7f2ff;
  background: rgba(64, 159, 255, 0.08);
  border: 1px solid rgba(153, 213, 255, 0.35);
  border-radius: 4px;
}
.action {
  color: #99d5ff;
  cursor: pointer;
}
</style>
