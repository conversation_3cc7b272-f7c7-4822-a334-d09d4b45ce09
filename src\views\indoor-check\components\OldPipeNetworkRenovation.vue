<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">隐患整改分析</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'administrative' }"
            @click="selectedOpt = 'administrative'"
          >
            行政区划
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'enterprise' }"
            @click="selectedOpt = 'enterprise'"
          >
            燃气企业
          </button>
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 左侧统计卡片区域 -->

        <div class="user-overview-container">
          <div v-for="(item, index) in userStats" :key="index" class="user-card">
            <div class="card-content">
              <div class="card-icon">
                <img :src="item.icon" alt="" />
                <div v-if="item.secondIcon" class="second-icon">
                  <img :src="item.secondIcon" alt="" />
                </div>
              </div>
              <div class="card-info">
                <div class="card-title">{{ item.title }}</div>
                <div class="card-value-container">
                  <div class="card-value">{{ item.value }}</div>
                  <div class="card-unit">{{ item.unit }}</div>
                </div>
              </div>
            </div>

            <!-- 移除两侧竖线图片，改为背景图 -->
          </div>
        </div>
        <!-- 右侧图表区域 -->
        <div class="chart-panel">
          <div class="chart-content">
            <div ref="chartRef" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

interface UserStat {
  type: string
  title: string
  value: string
  icon: string
  secondIcon?: string // 添加可选的 secondIcon 属性
  unit?: string // 添加可选的 unit 属性
}

const chartRef = ref<HTMLElement>()
const selectedOpt = ref<string>('administrative')
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引

const dataX = ['公开招标', '邀请招标', '竞争性谈判', '竞争性磋商', '询价', '单一来源采购', '框架协议采购']
const y = [350, 600, 260, 400, 200, 300, 260]
const y1 = [650, 280, 520, 260, 480, 180, 400]
const option = {
  title: {
    // 标题组件
    text: '单位：个', // 主标题文本
    left: '2%', // title 组件离容器左侧的距离
    top: '2%', // title 组件离容器上侧的距离
    textStyle: {
      color: '#fff', // 标题颜色
      fontSize: 12, // 标题像素
    },
  },
  grid: {
    left: '2%',
    right: '5%',
    bottom: '2%',
    top: '25%',
    containLabel: true,
  },
  tooltip: {
    // 提示框组件
    trigger: 'axis', // 触发类型
    textStyle: {
      fontSize: 12, // 文字像素
    },
  },
  legend: {
    // 图例组件
    left: '40%', // 图例组件离容器左侧的距离
    top: '2%', // 图例组件离容器上侧的距离
    itemWidth: 8, // 图例组件的宽度
    itemHeight: 8, // 图例组件的高度
    icon: 'stack', // 图例项的 icon
    textStyle: {
      color: '#fff', // 文字颜色
    },
  },
  xAxis: {
    data: dataX, // 数据
    min: 0, // 最小值
    type: 'category',
    boundaryGap: true, // 开始和结尾是否隔开
    axisLabel: {
      textStyle: {
        color: '#fff', // 文字颜色
        fontSize: 12, // 文字像素
      },
    },
    axisLine: {
      show: false, // 是否显示坐标轴轴线
    },
    axisTick: {
      show: false, // 去除刻度线
    },
  },
  yAxis: {
    type: 'value',
    min: 0, // 最小值
    axisLine: {
      show: false,
    },
    axisLabel: {
      textStyle: {
        color: '#fff', // 文字颜色
        fontSize: 12, // 文字像素
      },
    },
    // y轴分割线的颜色
    splitLine: {
      lineStyle: {
        color: 'rgba(216,216,216,0.45)', // 分割线的颜色
        type: 'dashed', // 虚线
        dashOffset: 0,
        width: 1,
        // ECharts 虚线样式通过lineDash设置段长
        // 但有的版本只认type: 'dashed'，如需更细可用如下写法：
        // lineDash: [2,2],
      },
    },
  },
  series: [
    {
      name: '已办结',
      type: 'line',
      symbolSize: 0, // 设置拐点大小
      label: {
        show: false, // 不显示线条折点处的值
      },
      // 填充颜色设置
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
          { offset: 0, color: 'rgba(255, 198, 26, 0)' },
          { offset: 1, color: 'rgba(255, 198, 26, 0.3)' },
        ]),
      },
      // 设置拐点颜色以及边框
      itemStyle: {
        color: '#FFC61A',
      },
      lineStyle: {
        width: 1, // 线的宽度
      },
      tooltip: {
        valueFormatter: function (value: any) {
          return value + '个'
        },
      },
      data: y1,
    },
    {
      name: '未办结',
      type: 'line',
      symbolSize: 0, // 设置拐点大小
      label: {
        show: false, // 不显示线条折点处的值
      },
      // 填充颜色设置
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
          { offset: 0, color: 'rgba(71, 235, 235, 0)' }, // 顶部
          { offset: 1, color: 'rgba(71, 235, 235, 0.3)' }, // 底部
        ]),
      },
      // 设置拐点颜色以及边框
      itemStyle: {
        color: '#47EBEB',
      },
      lineStyle: {
        width: 1, // 线的宽度
      },
      tooltip: {
        valueFormatter: function (value: any) {
          return value + '个'
        },
      },
      data: y,
    },
  ],
}

// 图标数据配置 - 使用图片路径
const iconData = {
  found: '/src/assets/indoor-icon/found.svg',
  done: '/src/assets/indoor-icon/done.svg',
  doneIntime: '/src/assets/indoor-icon/done-intime.svg',
  iconDown: '/src/assets/indoor-icon/user-bg.svg',
  union: '/src/assets/industry-icons/Union.svg',
}

const userStats = ref<UserStat[]>([
  {
    type: 'total',
    title: '发现隐患（个）',
    value: '1,245,678',
    icon: iconData.found,
    unit: '个',
  },
  {
    type: 'pipeline',
    title: '已整改（个）',
    value: '1,245,678',
    icon: iconData.done,
    unit: '个',
  },
  {
    type: 'pipeline',
    title: '及时整改（个）',
    value: '1,245,678',
    icon: iconData.doneIntime,
    unit: '个',
  },
])

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 50%;
  min-width: 420px;
}

.chart-content {
  flex: 1;
  padding: 4px;

  .chart-container {
    width: 100%;
    height: 100%;
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  width: 33.33%;
  flex-shrink: 0;
  height: 100%;
}

.user-card {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
  /* 卡片间距 */
  background: url('/src/assets/indoor-icon/right-bg.svg') no-repeat center/cover;
}
.user-card:not(:last-child) {
  margin-bottom: 16px;
}

.card-content {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  bottom: 12px;
  height: auto;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}

.card-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  > img {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    z-index: 1;
  }
  .second-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    z-index: 2;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-info {
  flex: 1;
  min-width: 0;
  height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  .card-title {
    width: 100%;
    height: 18px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC';
    font-size: 13px;
    line-height: 18px;
    text-align: left;
    margin-bottom: 2px;
  }
  .card-value-container {
    width: 100%;
    height: 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: baseline;
    .card-value {
      color: #ffffff;
      font-family: 'DINPro';
      font-size: 16px;
      line-height: 20px;
      font-weight: bold;
      margin-right: 2px;
    }
    .card-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 12px;
      line-height: 16px;
    }
  }
}

.card-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;

  > img {
    position: absolute;
    top: 0;
    left: 0;
    width: 48px;
    height: 48px;
    z-index: 1;
  }

  .second-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    z-index: 2;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-info {
  flex: 1;
  height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  .card-title {
    width: 100%;
    height: 20px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC';
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    margin-bottom: 4px;
  }

  .card-value-container {
    width: 100%;
    height: 24px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: baseline;

    .card-value {
      color: #ffffff;
      font-family: 'DINPro';
      font-size: 18px;
      line-height: 24px;
      font-weight: bold;
      margin-right: 4px;
    }

    .card-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.card-border-bottom {
  position: absolute;
  left: 30px;
  bottom: 0px;
  width: calc(100% - 60px);
  height: 12px;
}
.btn-group {
  display: flex;
  gap: 8px;
}
.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}
.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
</style>
