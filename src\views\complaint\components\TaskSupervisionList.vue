<template>
  <div class="panel-container">
    <div class="panel-header">任务督办列表</div>
    <div class="panel-content p-4">
      <div class="flex flex-col gap-3">
        <div
          v-for="item in list"
          :key="item.id"
          class="flex items-center gap-3 p-3 bg-white/0 rounded border border-white/10"
        >
          <img :src="item.icon" alt="" class="w-8 h-8" />
          <div class="flex-1">
            <div class="text-white/85">督办单号：{{ item.code }}</div>
            <div class="text-white/60 text-sm">{{ item.title }}</div>
            <div class="text-[#66ffff] text-xs mt-1">
              跟办人：{{ item.follower }}
              <span class="ml-4">剩余：{{ item.left }}天</span>
            </div>
          </div>
          <div>
            <img :src="item.statusIcon" class="w-6 h-6" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import urgent from '@/assets/complaint/urgent-alarm.png'
import timeout from '@/assets/complaint/timeout-alarm.png'

const list = [
  {
    id: 1,
    icon: urgent,
    statusIcon: urgent,
    code: 'DB20231115003',
    title: '肥乡区永济大街阀井燃气设备故障',
    follower: '王强',
    left: 3,
  },
  {
    id: 2,
    icon: timeout,
    statusIcon: timeout,
    code: 'DB20231115004',
    title: '肥乡区永济大街阀井燃气设备故障',
    follower: '刘伟',
    left: 5,
  },
]
</script>

<style scoped>
@import '@/styles/index.css';
</style>
