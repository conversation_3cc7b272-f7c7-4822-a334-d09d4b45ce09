<template>
  <div class="panel-container">
    <div class="panel-header">区域办结率排行</div>
    <div class="panel-content p-2">
      <div ref="container" class="w-full h-full"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { ref, onMounted, onBeforeUnmount } from 'vue'

const container = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null

const init = () => {
  if (!container.value) return
  chart = echarts.init(container.value)
  const areas = ['A区', 'B区', 'C区', 'D区', 'E区', 'F区', 'G区']
  const values = [92, 88, 84, 80, 76, 70, 66]
  chart.setOption({
    grid: { left: 100, right: 30, top: 18, bottom: 18 },
    xAxis: {
      type: 'value',
      max: 100,
      axisLabel: { color: '#66FFFF', formatter: '{value}%' },
      splitLine: { show: false },
    },
    yAxis: {
      type: 'category',
      data: areas.reverse(),
      axisLabel: { color: '#66FFFF' },
      axisTick: { show: false },
      axisLine: { show: false },
    },
    series: [
      {
        type: 'bar',
        data: values.reverse(),
        barWidth: 12,
        label: { show: true, position: 'right', color: '#FFD966', formatter: '{c}%' },
        itemStyle: {
          borderRadius: 6,
          color: new (echarts as any).graphic.LinearGradient(1, 0, 0, 0, [
            { offset: 0, color: '#FFD966' },
            { offset: 1, color: '#409FFF' },
          ]),
        },
      },
    ],
  })
}

onMounted(init)
onBeforeUnmount(() => chart?.dispose())
</script>

<style scoped>
@import '@/styles/index.css';
</style>
