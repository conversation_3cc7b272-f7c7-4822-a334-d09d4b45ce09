<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">企业隐患与整改统计</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'administrative' }"
            @click="selectedOpt = 'administrative'"
          >
            月
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'enterprise' }"
            @click="selectedOpt = 'enterprise'"
          >
            年
          </button>
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 左侧统计卡片区域 -->

        <!-- 右侧图表区域 -->
        <div class="chart-panel">
          <div class="chart-content">
            <div ref="chartRef" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
const selectedOpt = ref<string>('administrative')
let chart: echarts.ECharts | null = null
let tooltipTimer: ReturnType<typeof setInterval> | null = null

let xLabel = ['1', '2', '3', '4', '5']
const option = {
  tooltip: {},
  backgroundColor: 'transparent',
  animation: false,
  grid: {
    top: '25%',
    bottom: '10%',
    left: '8%',
    right: '8%',
  },
  legend: {
    show: true,
    icon: 'rect',
    top: '10%',
    left: 'center', // 图例居中
    textStyle: {
      color: '#fff',
      fontSize: 14, // 图例文字大小
      fontFamily: 'Noto Sans SC', // 图例字体
      fontWeight: 400,
      padding: [4, 8, 4, 8], // 图例项内边距
    },
    itemWidth: 20, // 图例色块宽度
    itemHeight: 10,
    itemGap: 24, // 图例项间距
    // 可根据需要添加更多样式
  },
  xAxis: {
    data: xLabel,
    axisLine: {
      show: true, //隐藏X轴轴线
      lineStyle: {
        color: '#11417a',
      },
    },
    axisTick: {
      show: false, //隐藏X轴刻度
    },
    axisLabel: {
      show: true,
      margin: 14,
      fontSize: 14,
      textStyle: {
        color: '#A3C0DF', //X轴文字颜色
      },
    },
  },
  yAxis: [
    {
      type: 'value',
      gridIndex: 0,
      min: 0,
      max: 100,
      interval: 25,
      splitLine: {
        show: true,
        lineStyle: {
          color: '#113763',
          width: 1,
        },
      },
      axisTick: {
        show: false, // 去掉纵刻度线
      },
      axisLine: {
        show: true, // 纵坐标线取消
      },
      axisLabel: {
        show: true,
        margin: 14,
        fontSize: 14,
        color: '#A3C0DF',
      },
    },
  ],
  series: [
    {
      name: 'A',
      type: 'bar',
      barWidth: 16,
      barGap: '30%',
      itemStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 198, 26, 1)' },
            { offset: 1, color: 'rgba(255, 121, 26, 1)' },
          ]),
        },
      },
      data: [20, 80, 100, 40, 34],
      z: 10,
      zlevel: 0,
      label: {
        show: false,
      },
    },
    {
      type: 'pictorialBar',
      itemStyle: {
        normal: {
          color: '#0F375F',
          borderRadius: [2, 2, 2, 2], // 分割柱加圆角
        },
      },
      symbolRepeat: 'fixed',
      symbolMargin: 3,
      symbol: 'rect',
      symbolClip: true,
      symbolSize: [16, 2],
      symbolPosition: 'start',
      symbolOffset: [-10, 0],
      data: [20, 80, 100, 40, 34],
      width: 25,
      z: 0,
      zlevel: 1,
    },
    {
      name: 'B',
      type: 'bar',
      barWidth: 16,
      barGap: '30%',
      itemStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(75, 217, 181, 1)' },
            { offset: 1, color: 'rgba(15, 138, 138, 1)' },
          ]),
        },
      },
      data: [20, 80, 100, 40, 34],
      z: 10,
      zlevel: 0,
      label: {
        show: false,
      },
    },
    {
      type: 'pictorialBar',
      itemStyle: {
        normal: {
          color: '#0F375F',
          borderRadius: [4, 4, 4, 4], // 分割柱加圆角
        },
      },
      symbolRepeat: 'fixed',
      symbolMargin: 3,
      symbol: 'rect',
      symbolClip: true,
      symbolSize: [16, 2],
      symbolPosition: 'start',
      symbolOffset: [10, 0],
      data: [20, 80, 100, 40, 34],
      width: 25,
      z: 0,
      zlevel: 1,
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 50%;
  min-width: 420px;
}

.chart-content {
  flex: 1;
  padding: 4px;

  .chart-container {
    width: 100%;
    height: 100%;
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  width: 33.33%;
  flex-shrink: 0;
  height: 100%;
}

.user-card {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
  /* 卡片间距 */
  background: url('/src/assets/indoor-icon/right-bg.svg') no-repeat center/cover;
}
.user-card:not(:last-child) {
  margin-bottom: 16px;
}

.card-content {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  bottom: 12px;
  height: auto;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}

.card-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  > img {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    z-index: 1;
  }
  .second-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    z-index: 2;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-info {
  flex: 1;
  min-width: 0;
  height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  .card-title {
    width: 100%;
    height: 18px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC';
    font-size: 13px;
    line-height: 18px;
    text-align: left;
    margin-bottom: 2px;
  }
  .card-value-container {
    width: 100%;
    height: 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: baseline;
    .card-value {
      color: #ffffff;
      font-family: 'DINPro';
      font-size: 16px;
      line-height: 20px;
      font-weight: bold;
      margin-right: 2px;
    }
    .card-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 12px;
      line-height: 16px;
    }
  }
}

.card-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;

  > img {
    position: absolute;
    top: 0;
    left: 0;
    width: 48px;
    height: 48px;
    z-index: 1;
  }

  .second-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    z-index: 2;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-info {
  flex: 1;
  height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  .card-title {
    width: 100%;
    height: 20px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC';
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    margin-bottom: 4px;
  }

  .card-value-container {
    width: 100%;
    height: 24px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: baseline;

    .card-value {
      color: #ffffff;
      font-family: 'DINPro';
      font-size: 18px;
      line-height: 24px;
      font-weight: bold;
      margin-right: 4px;
    }

    .card-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.card-border-bottom {
  position: absolute;
  left: 30px;
  bottom: 0px;
  width: calc(100% - 60px);
  height: 12px;
}
.btn-group {
  display: flex;
  gap: 8px;
}
.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}
.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}

.btn-group {
  display: flex;
  gap: 8px;
}
.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}
.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
</style>
