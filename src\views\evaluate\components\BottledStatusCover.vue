<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">企业安全五维评估</div>
      <div class="header-dropdown">
        <Select v-model:model-value="selectedOpt">
          <SelectTrigger class="text-sm dropdown-btn" size="sm">
            <SelectValue placeholder="选择区域" />
          </SelectTrigger>
          <SelectContent class="text-[#99D5FF]">
            <SelectGroup>
              <!-- <SelectLabel>Fruits</SelectLabel> -->
              <SelectItem value="area1">区域一</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
    <div class="flex p-4 panel-content">
      <div ref="chartRef" class="w-[450px] h-full"></div>
      <div class="w-[240px]">
        <div class="text-base text-center text-[#47EBEB]">评估参数</div>
        <ul>
          <li
            class="flex justify-between leading-8 text-sm border-b-1 border-[#1AB2ff]/60"
            v-for="item in list"
            :key="item.id"
          >
            <p>{{ item.name }}</p>
            <p>{{ item.value }}</p>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import * as echarts from 'echarts'

const selectedOpt = ref<string>('')
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const list = ref([
  {
    name: '设备设施',
    value: 88,
    id: 1,
  },
  {
    name: '用户安全',
    value: 88,
    id: 2,
  },
  {
    name: '服务质量',
    value: 88,
    id: 3,
  },
  {
    name: '应急救援',
    value: 88,
    id: 4,
  },
  {
    name: '安全管理',
    value: 88,
    id: 5,
  },
])

const option = {
  color: ['#FF791A'],
  radar: {
    indicator: list.value.map(item => ({ name: item.name, max: 100 })),
    axisName: {
      show: true,
      color: '#fff',
    },
    axisLine: {
      lineStyle: {
        color: '#2A637D',
      },
    },
    splitLine: {
      lineStyle: {
        color: '#2A637D',
      },
    },
    splitArea: {
      show: false,
    },
  },
  series: [
    {
      name: '',
      type: 'radar',
      data: [
        {
          value: list.value.map(item => item.value),
          // name: 'Allocated Budget',
          areaStyle: {
            color: 'rgba(255, 121, 26, 0.15)',
          },
        },
      ],
    },
  ],
}

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  // startHighlightAnimation()
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initchart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
@import '@/styles/index.css';

.header-title {
  color: white;
  font-size: 16px;
  font-weight: 500;
  flex: 1;
  margin-left: 8px;
}

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.gauge-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  background: url('@/assets/household-inspection/indicator-con-bg.png') no-repeat center;
  background-size: cover;
}

.gauge-circle-bg {
  width: 72px;
  height: 60px;
  background: url('@/assets/household-inspection/indicator-bg.png') no-repeat center;
  background-size: cover;
  will-change: transform;
  /* animation: spin 5s linear infinite; */
}

.gauge-value {
  color: #fff;
  font-family: DINPro;
  font-size: 24px;
  font-weight: bold;
  z-index: 1;
}

.gauge-label {
  color: #66ffff;
  font-size: 14px;
  text-align: center;
}
</style>
