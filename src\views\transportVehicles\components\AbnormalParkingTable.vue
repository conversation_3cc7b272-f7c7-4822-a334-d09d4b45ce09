<template>
  <div class="panel-container-col" style="margin: 0">
    <div class="panel-header">异常停车记录</div>
    <div class="p-4 panel-content">
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 26%" />
          <col style="width: 22%" />
          <col style="width: 16%" />
          <col style="width: auto" />
          <col style="width: 12%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">停车时间</TableHead>
            <TableHead class="font-bold text-white">车辆型号</TableHead>
            <TableHead class="font-bold text-white">车辆号码</TableHead>
            <TableHead class="font-bold text-white">所属服务企业</TableHead>
            <TableHead class="font-bold text-white">操作</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${7 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 26%" />
            <col style="width: 22%" />
            <col style="width: 16%" />
            <col style="width: auto" />
            <col style="width: 12%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.time }}</TableCell>
              <TableCell>{{ item.region }}</TableCell>
              <TableCell>{{ item.plate }}</TableCell>
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.company }}</TableCell>
              <TableCell class="action">查看</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

const rows = ref(
  Array.from({ length: 12 }).map(() => ({
    time: '2025-08-07 08:45:22',
    region: '这是对应内容信息',
    plate: '冀D·A1234',
    company: '这是对应内容信息',
  })),
)

const scrollList = computed(() => [...rows.value, ...rows.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 48
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight
    if (scrollTop.value >= rows.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';
.action {
  color: #99d5ff;
  cursor: pointer;
}
</style>
