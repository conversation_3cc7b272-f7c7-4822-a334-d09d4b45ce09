<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">预警事件月度分析</div>
      <div class="header-dropdown">
        <Select v-bind:model-value="selectedOpt">
          <SelectTrigger class="text-sm dropdown-btn" size="sm">
            <SelectValue placeholder="选择月份" />
          </SelectTrigger>
          <SelectContent class="text-[#99D5FF]">
            <SelectGroup>
              <SelectItem value="1">一月</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="w-full h-full">
        <div ref="chartRef" class="chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const selectedOpt = ref('')

// 模拟数据
const chartData = {
  months: ['01月', '02月', '03月', '04月', '05月', '06月', '07月', '08月', '09月', '10月', '11月', '12月'],
  type1: [20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20],
  type2: [20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20],
  type3: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
}

// 计算堆叠数据的总和
const calculateStackedData = () => {
  return {
    type1Total: chartData.type1,
    type2Total: chartData.type1.map((val, idx) => val + chartData.type2[idx]),
    type3Total: chartData.type1.map((val, idx) => val + chartData.type2[idx] + chartData.type3[idx]),
  }
}

const option = {
  color: ['#66FFFF', '#99B2FF', '#FFC61A'],
  grid: {
    top: '15%',
    left: 40,
    right: 20,
    bottom: 0,
    containLabel: true,
  },
  legend: {
    data: ['类型1', '类型2', '类型3'],
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 20,
    itemHeight: 10,
    icon: 'rect',
    itemGap: 24,
    top: 0,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        color: '#47EBEB',
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
  },
  xAxis: {
    type: 'category',
    data: chartData.months,
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#ffffff',
      fontSize: 12,
    },
  },
  yAxis: {
    type: 'value',
    name: '单位：件',
    max: 50,
    interval: 10,
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#ffffff',
      fontSize: 12,
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.1)',
        type: 'dashed',
      },
    },
  },
  series: [
    {
      name: '类型1',
      type: 'bar',
      stack: 'total',
      data: chartData.type1,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(71, 235, 235, 0.6)' },
          { offset: 1, color: 'rgba(71, 235, 235, 0.3)' },
        ]),
      },
    },
    {
      name: '类型2',
      type: 'bar',
      stack: 'total',
      data: chartData.type2,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(153, 178, 255, 0.6)' },
          { offset: 1, color: 'rgba(153, 178, 255, 0.3)' },
        ]),
      },
    },
    {
      name: '类型3',
      type: 'bar',
      stack: 'total',
      data: chartData.type3,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(255, 198, 26, 0.6)' },
          { offset: 1, color: 'rgba(255, 198, 26, 0.3)' },
        ]),
      },
      barWidth: 20,
    },
    {
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'rect',
      symbolPosition: 'end',
      symbolSize: [20, 2],
      symbolOffset: [0, 0],
      z: 10,
      itemStyle: {
        color: '#66FFFF',
      },
      tooltip: { show: false },
      data: calculateStackedData().type1Total,
    },
    {
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'rect',
      symbolPosition: 'end',
      symbolSize: [20, 2],
      symbolOffset: [0, 0],
      z: 10,
      itemStyle: {
        color: '#99B2FF',
      },
      tooltip: { show: false },
      data: calculateStackedData().type2Total,
    },
    {
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'rect',
      symbolPosition: 'end',
      symbolSize: [20, 2],
      symbolOffset: [0, 0],
      z: 10,
      itemStyle: {
        color: '#FFC61A',
      },
      tooltip: { show: false },
      data: calculateStackedData().type3Total,
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = option.xAxis.data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  // 清理所有定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
