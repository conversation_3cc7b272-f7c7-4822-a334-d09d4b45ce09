<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">监督检查</div>
      <div class="header-dropdown">
        <Select v-bind:model-value="selectedOpt">
          <SelectTrigger class="text-sm dropdown-btn" size="sm">
            <SelectValue placeholder="任务检查" />
          </SelectTrigger>
          <SelectContent class="text-[#99D5FF]">
            <SelectGroup>
              <SelectItem value="2025上">2025年上半年度抽查</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

const selectedOpt = ref<string>('2025上')
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

const xAxis = ['肥乡镇', '天台山镇', '辛安镇镇', '大寺上镇', '东漳堡镇', '西吕营镇', '毛演堡镇', '其他乡镇']

const option = {
  animation: true,
  backgroundColor: 'transparent',
  grid: {
    left: '5%',
    right: '0%',
    top: '15%',
    bottom: '0%',
    containLabel: true,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        shadowColor: 'rgba(11, 46, 115, 0.3)',
        shadowBlur: 10,
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
  },
  legend: {
    data: ['全部', '办结'],
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 20,
    itemHeight: 10,
    icon: 'rect',
    itemGap: 24,
    top: 0,
    // itemStyle: {
    //   color: ['#E19760', '#99D5FF']
    // }
  },
  // show: false,
  xAxis: {
    type: 'category',
    data: xAxis,
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.45)',
      },
    },
    axisLabel: {
      color: '#fff',
      fontSize: 14,
    },
    axisTick: {
      show: false,
    },
  },
  yAxis: {
    type: 'value',
    name: '单位：万次',
    nameTextStyle: {
      color: '#fff',
      fontSize: 14,
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.45)',
      },
    },
    axisLabel: {
      color: '#fff',
      fontSize: 14,
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(255, 255, 255, 0.1)',
        type: 'dashed',
      },
    },
  },
  series: [
    {
      name: '全部',
      type: 'bar',
      data: [0.7, 0.83, 0.78, 0.63, 0.61, 0.37, 0.36, 4.98],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(255, 198, 26, 0.15)' },
          { offset: 1, color: 'rgba(255, 198, 26, 0.75)' },
        ]),
      },
      // 为图例设置独立的样式
      legendHoverLink: false,
      legend: {
        itemStyle: {
          color: '#FFC61A', // 图例显示的颜色
        },
      },
      barWidth: 20,
      z: 2,
    },
    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'rect',
      symbolPosition: 'end',
      symbolSize: [20, 2],
      symbolOffset: [-12, 0],
      z: 10,
      itemStyle: {
        color: '#FFC61A',
      },
      tooltip: { show: false },
      data: [0.7, 0.83, 0.78, 0.63, 0.61, 0.37, 0.36, 4.98],
    },
    // 贯穿的虚线
    {
      type: 'pictorialBar',
      symbol: 'rect',
      symbolSize: [1, '100%'],
      symbolPosition: 'center',
      symbolOffset: [-12, 0],
      z: 4,
      itemStyle: {
        color: 'transparent',
        borderColor: '#FFC61A',
        borderWidth: 1,
        borderType: 'solid',
      },
      tooltip: { show: false },
      data: [0.7, 0.83, 0.78, 0.63, 0.61, 0.37, 0.36, 4.98],
    },
    {
      name: '办结',
      type: 'bar',
      data: [0.65, 0.8, 0.77, 0.6, 0.6, 0.33, 0.34, 3.56],
      // itemStyle: {
      //   color: '#99D5FF'
      // },
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(77, 203, 98, 0.15)' },
          { offset: 1, color: 'rgba(77, 203, 98, 0.75)' },
        ]),
      },
      // 为图例设置独立的样式
      legendHoverLink: false,
      // emphasis: {
      //   itemStyle: {
      //     color: '#4DCB62', // 图例显示的颜色
      //   },
      // },
      barWidth: 20,
      z: 2,
    },
    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'rect',
      symbolPosition: 'end',
      symbolSize: [20, 2],
      symbolOffset: [12, 0],
      z: 10,
      itemStyle: {
        color: '#4DCB62',
      },
      tooltip: { show: false },
      data: [0.65, 0.8, 0.77, 0.6, 0.6, 0.33, 0.34, 3.56],
    },
    // 贯穿的虚线
    {
      type: 'pictorialBar',
      symbol: 'rect',
      symbolSize: [1, '100%'],
      symbolPosition: 'center',
      symbolOffset: [12, 0],
      z: 4,
      itemStyle: {
        color: 'transparent',
        borderColor: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(192, 255, 179, .65)' },
            { offset: 1, color: 'rgba(192, 255, 179, .15)' },
          ],
        },
        borderWidth: 1,
        borderType: 'solid',
      },
      tooltip: { show: false },
      data: [0.65, 0.8, 0.77, 0.6, 0.6, 0.33, 0.34, 3.56],
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = option.xAxis.data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  // 清理所有定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 0;
  outline: none;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
