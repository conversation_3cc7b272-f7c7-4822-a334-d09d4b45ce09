<template>
  <div class="panel-container-col">
    <div class="panel-header">气瓶充装统计</div>
    <div class="p-4 panel-content">
      <div class="chart-section">
        <div ref="chartRef" class="donut-chart"></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

let data = [
  {
    label: '周一',
    value: [
      { name: '东宏液化气', value: 26.8 },
      { name: '五申液化气', value: 27.4 },
    ],
  },
  {
    label: '周二',
    value: [
      { name: '东宏液化气', value: 36.5 },
      { name: '五申液化气', value: 36.5 },
    ],
  },
  {
    label: '周三',
    value: [
      { name: '东宏液化气', value: 29.2 },
      { name: '五申液化气', value: 26 },
    ],
  },
  {
    label: '周四',
    value: [
      { name: '东宏液化气', value: 27 },
      { name: '五申液化气', value: 25.8 },
    ],
  },
  {
    label: '周五',
    value: [
      { name: '东宏液化气', value: 28.8 },
      { name: '五申液化气', value: 25.8 },
    ],
  },
  {
    label: '周六',
    value: [
      { name: '东宏液化气', value: 37.2 },
      { name: '五申液化气', value: 35.2 },
    ],
  },
  {
    label: '周日',
    value: [
      { name: '东宏液化气', value: 28.3 },
      { name: '五申液化气', value: 25.3 },
    ],
  },
]
let color = ['#47EBEB', '#6E30E8', '#C0FFB3']

const option = {
  color,
  grid: {
    top: 40,
    left: 24,
    bottom: 30,
    right: 8,
  },
  legend: {
    top: 0,
    // right: 0,
    // icon: 'rect',
    // itemWidth: 10,
    // itemHeight: 10,
    // itemStyle: {
    //   borderWidth: 0,
    // },
    inactiveBorderWidth: 0,
    textStyle: {
      color: '#fff',
    },
  },
  tooltip: {
    show: true,
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        color: '#47EBEB',
      },
    },
    backgroundColor: 'rgba(11, 46, 115, 0.6)',
    borderColor: '#409FFF',
    backdropFilter: 'blur(4px)',
    textStyle: {
      color: '#fff',
    },
    formatter: (p: any) => {
      if (p[0].dataIndex === 0 || p[0].dataIndex === data.length + 1) {
        return ''
      } else {
        return (
          p[0].axisValue +
          p
            .map(
              (i: any) =>
                `<br/><div style="display:inline-block;margin-right:4px;width:12px;height:12px;background:${i.color};border-radius: 50%;"></div><div style="display:inline-block;">${i.name}: ${i.value}</div>`,
            )
            .join('')
        )
      }
    },
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        color: '#fff',
        type: 'solid',
        opacity: 0.3,
      },
    },
    boundaryGap: false,
    data: ['', ...data.map(i => i.label), ''],
  },
  yAxis: {
    type: 'value',
    name: '单位：Mpa',
    nameTextStyle: {
      color: '#fff',
      align: 'left',
    },
    splitLine: {
      lineStyle: {
        color: '#fff',
        opacity: 0.3,
        type: 'dashed',
      },
    },
    axisLabel: {
      color: '#fff',
    },
  },
  series: data[0].value.map((item, index) => ({
    name: item.name,
    type: 'line',
    smooth: 0.5,
    symbol: 'circle',
    symbolSize: 8,
    lineStyle: {
      color: color[index],
    },
    itemStyle: {
      color: (p: any) => {
        if (p.dataIndex !== 0 && p.dataIndex !== data.length + 1) return color[index]
      },
      borderColor: '#fff',
      borderWidth: 1,
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0, color: color[index] + '4D' },
          { offset: 1, color: color[index] + '00' },
        ],
      },
    },
    data: [
      data.reduce((a, b) => a + b.value[index].value, 0) / data.length,
      ...data.map(i => i.value[index]),
      data.reduce((a, b) => a + b.value[index].value, 0) / data.length,
    ],
  })),
}

const initchart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(option)
  startHighlightAnimation()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = option.series[1].data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0, // 在第一个系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0, // 作用在第二个系列（数据饼图）上
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initchart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-section {
  width: 100%;
  height: 376px;
}

.donut-chart {
  width: 100%;
  height: 100%;
}
</style>
