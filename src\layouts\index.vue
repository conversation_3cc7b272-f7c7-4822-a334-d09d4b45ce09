<template>
  <div class="contain">
    <screen-header></screen-header>
    <slot />
    <screen-map ref="mapRef"></screen-map>
    <screen-warning :open="isShowWarning"></screen-warning>
  </div>
</template>
<script setup lang="ts">
import screenHeader from './components/Header.vue'
import screenWarning from './components/Warning.vue'
import screenMap from '../components/Map.vue'
import { ref, provide } from 'vue'

const isShowWarning = ref(false)
const mapRef = ref()

// 提供地图引用给子组件使用
provide('mapRef', mapRef)
</script>
<style scoped>
.contain {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
