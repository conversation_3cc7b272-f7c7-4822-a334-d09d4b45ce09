<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <IndustryOverview />
      <HouseholdInspection />
      <RiskHazardMonitoring />
    </div>

    <!-- 中央地图区域 -->
    <div class="center-panel">
      <!-- 地图控制组件 -->
      <LayersTool v-model="layersData" @layer-change="handleLayerChange" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <PipelineOverview />
      <OldPipeNetworkRenovation />
      <div class="flex gap-6">
        <BottledGasStatus />
        <CylinderFillingMonitoring />
      </div>
    </div>

    <!-- 弹窗 -->
    <IndustryInfoDialog :open="industryInfoOpen" @close="industryInfoOpen = false" />
    <StationDialog :open="stationInfoOpen" @close="stationInfoOpen = false" />
    <PipelineDialog :open="pipelineInfoOpen" @close="pipelineInfoOpen = false" />
    <AreaDialog :open="areaInfoOpen" @close="areaInfoOpen = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, inject } from 'vue'
import IndustryOverview from './components/IndustryOverview.vue'
import HouseholdInspection from './components/HouseholdInspection.vue'
// import SupervisionInspection from './components/SupervisionInspection.vue'
import RiskHazardMonitoring from './components/RiskHazardMonitoring.vue'
import OldPipeNetworkRenovation from './components/OldPipeNetworkRenovation.vue'
import BottledGasStatus from './components/BottledGasStatus.vue'
import CylinderFillingMonitoring from './components/CylinderFillingMonitoring.vue'
import PipelineOverview from './components/PipelineOverview.vue'

import LayersTool from '@/components/LayersTool.vue'
import IndustryInfoDialog from '@/components/ue-dialog/IndustryInfoDialog.vue'
import StationDialog from '@/components/ue-dialog/StationDialog.vue'
import PipelineDialog from '@/components/ue-dialog/PipelineDialog.vue'
import AreaDialog from '@/components/ue-dialog/AreaDialog.vue'
import { layerData } from './layerData'
import type { LayerChangeEvent } from '@/types/mapLayers'
import { useResettableLayers } from '@/hooks/useResettableLayers'

// 导入图片资源
import gasIcon from '@/assets/map/gas-icon.png'
import stationIcon from '@/assets/map/station-icon.png'

// 弹窗状态
const industryInfoOpen = ref(false)
const stationInfoOpen = ref(false)
const pipelineInfoOpen = ref(false)
const areaInfoOpen = ref(false)

// 图层数据
const { layersData, resetLayersState } = useResettableLayers(layerData)

// 注入地图引用
const mapRef: any = inject('mapRef')

// 处理燃气企业图层
const handleGasCompanyLayer = async (checked: boolean) => {
  if (!mapRef.value) return

  if (checked) {
    try {
      const response = await fetch('/mock/gasCompany.json')
      const data: { data: any[] } = await response.json()
      const markersConfig = data.data.map(item => ({
        id: item.properties.qymc,
        position: { lng: parseFloat(item.geometry.coordinates[0]), lat: parseFloat(item.geometry.coordinates[1]) },
        iconUrl: gasIcon,
        extData: { ...item.properties },
        content: `
          <div style="position:relative;width:24px;height:24px;border: 1px solid #309CE8;background:rgba(20, 51, 82, 0.60) url(\'${gasIcon}\') center no-repeat; background-size:16px 16px;">
          </div>
        `,
        getInfoWindowContent: (infoObj: any) => `
            <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
              <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">${infoObj.qymc || '燃气企业'}</div>
              <div style="color: #fff;font-size: 14px;">负责人：${infoObj.fddbrxm || '未知'}</div>
              <div style="color: #fff;font-size: 14px;">地址：${infoObj.zzzs || '未知'}</div>
              <div style="color: #fff;font-size: 14px;">状态：${infoObj.jyzt || '未知'}</div>
            </div>
          `,
      }))
      mapRef.value.addGenericMarkers('gasCompanyLayer', markersConfig)
    } catch (error) {
      console.error('加载燃气企业数据失败:', error)
    }
  } else {
    mapRef.value.clearLayer('gasCompanyLayer')
  }
}

// 处理场站图层
const handleStationLayer = async (checked: boolean) => {
  if (!mapRef.value) return

  if (checked) {
    try {
      const response = await fetch('/mock/station.json')
      const data: { data: any[] } = await response.json()
      const markersConfig = data.data.map(item => ({
        id: item.properties.czmc,
        position: { lng: parseFloat(item.geometry.coordinates[0]), lat: parseFloat(item.geometry.coordinates[1]) },
        iconUrl: stationIcon,
        extData: { ...item.properties },
        content: `
          <div style="position:relative;width:24px;height:24px;border: 1px solid #309CE8;background:rgba(20, 51, 82, 0.60) url(\'${stationIcon}\') center no-repeat; background-size:16px 16px;">
          </div>
        `,
        getInfoWindowContent: (infoObj: any) => `
            <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
              <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">${infoObj.czmc || '场站'}</div>
              <div style="color: #fff;font-size: 14px;">企业：${infoObj.qymc || '未知'}</div>
              <div style="color: #fff;font-size: 14px;">负责人：${infoObj.fzrmc || '未知'}</div>
              <div style="color: #fff;font-size: 14px;">位置：${infoObj.wz || '未知'}</div>
              <div style="color: #fff;font-size: 14px;">联系电话：${infoObj.fzrlxdh || '未知'}</div>
            </div>
          `,
      }))
      mapRef.value.addGenericMarkers('stationLayer', markersConfig)
    } catch (error) {
      console.error('加载场站数据失败:', error)
    }
  } else {
    mapRef.value.clearLayer('stationLayer')
  }
}

// 处理管线图层
const handlePipelineLayer = async (itemId: string | undefined, checked: boolean) => {
  if (!mapRef.value) return

  try {
    const response = await fetch('/mock/pipeline.json')
    const data: { 'high-pressure': any[]; 'medium-pressure': any[] } = await response.json()

    const addHighPressure = (checked: boolean) => {
      if (checked) {
        const highPressureConfig = data['high-pressure'].map(item => ({
          id: item.properties.gxmc + '-high',
          path: JSON.parse(item.geometry).coordinates.map((coord: number[]) => ({ lng: coord[0], lat: coord[1] })),
          strokeColor: '#FF0000',
          extData: { ...item.properties, pressureType: 'high-pressure' },
          getInfoWindowContent: (infoObj: any) => `
            <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
              <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">${infoObj.gxmc || '管线'}</div>
              <div style="color: #fff;font-size: 14px;">类型：${infoObj.gxlx || '未知'}</div>
              <div style="color: #fff;font-size: 14px;">压力：高压</div>
            </div>
          `,
        }))
        mapRef.value.addGenericPolylines('highPressurePipelineLayer', highPressureConfig)
      } else {
        mapRef.value.clearLayer('highPressurePipelineLayer')
      }
    }

    const addMediumPressure = (checked: boolean) => {
      if (checked) {
        const mediumPressureConfig = data['medium-pressure'].map(item => ({
          id: item.properties.gxmc + '-medium',
          path: JSON.parse(item.geometry).coordinates.map((coord: number[]) => ({ lng: coord[0], lat: coord[1] })),
          strokeColor: '#FFFF00',
          extData: { ...item.properties, pressureType: 'medium-pressure' },
          getInfoWindowContent: (infoObj: any) => `
            <div style="padding: 8px 12px; white-space: nowrap;border-radius: 4px;border: 1px solid #309CE8;background: rgba(20, 51, 82, 0.60);backdrop-filter: blur(2px);z-index:1000;">
              <div style="color: #47EBEB;font-size: 16px;font-weight: 500;">${infoObj.gxmc || '管线'}</div>
              <div style="color: #fff;font-size: 14px;">类型：${infoObj.gxlx || '未知'}</div>
              <div style="color: #fff;font-size: 14px;">压力：中压</div>
            </div>
          `,
        }))
        mapRef.value.addGenericPolylines('mediumPressurePipelineLayer', mediumPressureConfig)
      } else {
        mapRef.value.clearLayer('mediumPressurePipelineLayer')
      }
    }

    if (!itemId) {
      // 组全选/全不选
      addHighPressure(checked)
      addMediumPressure(checked)
    } else if (itemId === 'high-pressure') {
      addHighPressure(checked)
    } else if (itemId === 'medium-pressure') {
      addMediumPressure(checked)
    }
  } catch (error) {
    console.error('加载管线数据失败:', error)
  }
}

// 处理图层变化
const handleLayerChange = async (event: LayerChangeEvent) => {
  const { groupId, itemId, checked } = event

  if (!mapRef.value) return

  // 获取 AMap 实例，用于创建 Pixel 等
  const AMapInstance = mapRef.value.getAMapInstance()
  if (!AMapInstance) {
    console.error('AMap instance not available.')
    return
  }

  switch (groupId) {
    case 'gasCompany':
      handleGasCompanyLayer(checked)
      break
    case 'station':
      handleStationLayer(checked)
      break
    case 'pipeline':
      handlePipelineLayer(itemId, checked)
      break
    // ... 其他图层类型
  }
}

onMounted(() => {
  // 页面加载时重置图例状态并触发所有已选中图层的显示
  resetLayersState()
  // 遍历 layersData，对所有 checked 为 true 的图层触发 handleLayerChange
  Object.entries(layersData.value).forEach(([groupId, group]) => {
    if (group.checked === true) {
      handleLayerChange({ groupId, checked: true })
    } else if (group.checked === 'indeterminate' && group.children) {
      group.children.forEach(child => {
        if (child.checked) {
          handleLayerChange({ groupId, itemId: child.id, checked: true })
        }
      })
    }
  })
})

onUnmounted(() => {
  // 清除所有图层
  if (mapRef.value) {
    mapRef.value.clearLayer('gasCompanyLayer')
    mapRef.value.clearLayer('stationLayer')
    mapRef.value.clearLayer('highPressurePipelineLayer')
    mapRef.value.clearLayer('mediumPressurePipelineLayer')
    // ... 其他图层
  }

  // 页面卸载时重置图例状态
  resetLayersState()
})
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 96px;
  left: 24px;
  right: 24px;
  height: calc(100% - 96px);
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 744px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.left-panel {
  left: 0;
}

.right-panel {
  right: 0;
}

.center-panel {
  position: absolute;
  top: 0;
  left: 768px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
