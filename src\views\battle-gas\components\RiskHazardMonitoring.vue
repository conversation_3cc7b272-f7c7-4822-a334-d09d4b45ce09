<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">气瓶用户分析</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 左侧统计卡片区域 -->
        <div class="user-overview-container">
          <div v-for="(item, index) in userStats" :key="index" class="user-card">
            <div class="card-content">
              <div class="card-icon">
                <img :src="item.icon" alt="" />
                <div v-if="item.secondIcon" class="second-icon">
                  <img :src="item.secondIcon" alt="" />
                </div>
              </div>
              <div class="card-info">
                <div class="card-title">{{ item.title }}</div>
                <div class="card-value-container">
                  <div class="card-value">{{ item.value }}</div>
                  <div class="card-unit">{{ item.unit }}</div>
                </div>
                <div class="card-compare">
                  同比：
                  <span class="card-compare-number">{{ item.compare ?? '--' }}</span>
                </div>
              </div>
            </div>
            <img :src="iconData.union" alt="" class="card-border-bottom" />
            <div class="card-side-lines">
              <img :src="iconData.vectorLeft" alt="" class="side-line left" />
              <img :src="iconData.vectorRight" alt="" class="side-line right" />
            </div>
          </div>
        </div>
        <!-- 右侧图表区域 -->
        <div class="chart-panel">
          <div class="chart-content">
            <div ref="chartRef" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

interface UserStat {
  type: string
  title: string
  value: string
  icon: string
  secondIcon?: string // 添加可选的 secondIcon 属性
  unit?: string // 添加可选的 unit 属性
  compare?: string // 添加可选的 compare 属性
}

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// tooltip 轮播定时器
let tooltipTimer: ReturnType<typeof setInterval> | null = null
// 当前展示的 tooltip 索引
let currentIndex = -1

// Mock数据
let xData = ['2019', '2020']

const option = {
  数据节点1: 30,
  数据节点2: 52,
  color: ['rgba(245, 42, 118,', 'rgba(255, 255, 0,', 'rgba(14, 248, 254,'],
  backgroundColor: 'transparent',
  dataset: {
    source: [
      ['product', 'data', 'data2'],
      ['面料', '100', '30'],
      ['里料', '50', '60'],
    ],
  },
  grid: {
    left: '8%',
    right: '5%',
    bottom: '0%',
    top: '25%',
    containLabel: true,
  },

  tooltip: {
    trigger: 'axis',
    legend: {
      show: true,
      right: 10,
      data: ['有销库存满足率', '日环比'],
      itemWidth: 4,
      itemHeight: 12,
      itemGap: 15,
      icon: 'circle',
      textStyle: {
        color: 'rgba(14, 248, 254,1)',
        fontSize: 12,
      },
    },
    borderWidth: 1,
    backgroundColor: '#0e2b33',
    borderColor: 'rgba(14, 248, 254, 1)',
    formatter: function (A: any) {
      var htm =
        "<div style='color:#fff'>" +
        A[0].name +
        "</div><div style='color:rgba(14, 248, 254,1)'>有销库存满足率：" +
        A[0].value[1].toLocaleString() +
        "%</div><div style='color:rgba(14, 248, 254,1)'>日环比：" +
        A[0].value[2].toLocaleString() +
        '%</div>'
      return htm
    },
    extraCssText: 'z-index:96',
  },
  xAxis: {
    type: 'category',
    axisTick: {
      show: true,
      alignWithLabel: true,
      length: 6,
      lineStyle: {
        color: '#fff',
        width: 1,
      },
    },
    axisLine: {
      show: true,
      onZero: false,
      lineStyle: {
        color: '#fff',
        width: 0.5,
      },

      symbol: 'none',
      symbolOffset: [0, 10],
    },
    splitLine: {
      show: false,
    },
    axisLabel: {
      show: true,
      interval: 0,
      margin: 25,
      color: '#fff',

      fontSize: 12,
      formatter: function (value: any) {
        return value
      },
    },
  },
  yAxis: [
    {
      show: true,
      type: 'value',
      name: '单位：项',
      nameTextStyle: {
        color: '#fff',
        fontSize: 12,
        padding: [0, 0, 8, 0],
      },
      nameLocation: 'end',
      nameGap: 18,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(166, 207, 255, 0.4)',
          type: 'dashed',
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12,
      },
    },
    {
      show: true,
      type: 'value',
      min: 0,
      max: 100,
      position: 'right',
      axisLabel: {
        formatter: '{value}%',
        color: '#fff',
        fontSize: 12,
      },
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
  ],
  series: [
    {
      name: '有销库存满足率', //顶
      type: 'pictorialBar',
      symbolSize: [25, 10],
      symbolOffset: [0, -10],
      symbolPosition: 'end',
      z: 12,
      itemStyle: {
        color: '#49D1AF',
        barBorderRadius: 30,
      },
      label: {
        show: false,
      },
    },
    {
      name: '有销库存满足率', //底
      type: 'pictorialBar',
      symbolSize: [25, 10],
      symbolOffset: [0, 5],
      z: 12,
      itemStyle: {
        color: '#49D1AF',
        barBorderRadius: 30,
      },
    },
    {
      name: '有销库存满足率', //底
      type: 'pictorialBar',
      symbolSize: [36, 12],
      symbolOffset: [0, 8],
      itemStyle: {
        normal: {
          color: 'transparent',
          borderColor: 'rgba(14, 248, 254,1)',
          borderWidth: 2,
        },
      },
    },
    {
      name: '有销库存满足率', //底
      type: 'pictorialBar',
      symbolSize: [52, 18],
      symbolOffset: [0, 14],
      itemStyle: {
        normal: {
          color: 'transparent',
          borderColor: 'rgba(14, 248, 254,1)',
          borderWidth: 2,
        },
      },
    },
    {
      name: '日环比',
      type: 'bar',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(0, 149, 178, 0.15)',
          },
          {
            offset: 1,
            color: 'rgba(73, 209, 175, 0.8)',
          },
        ]),
      },
      showBackground: true,
      backgroundStyle: {
        color: 'rgba(0,0,0,0)',
      },
      barWidth: 25,
      barGap: '-100%',
    },
    {
      name: 'SKC满足率',
      type: 'line',
      yAxisIndex: 1,
      smooth: true,
      showAllSymbol: false,
      showSymbol: false,
      symbol: 'circle',
      symbolSize: 3,
      lineStyle: {
        normal: {
          show: true,
          color: '#ffea38',
        },
      },
      encode: {
        y: 'data2',
      },
      label: {
        show: false,
      },
      itemStyle: {
        color: '#ffea38',
      },
    },
  ],
}

// 图标数据配置 - 使用图片路径
const iconData = {
  totalUser: '/src/assets/battle-gas/total-user.svg',
  inUser: '/src/assets/battle-gas/in-user.svg',

  vectorLeft: '/src/assets/industry-icons/Vector-Left.svg',
  vectorRight: '/src/assets/industry-icons/Vector-right.svg',
  union: '/src/assets/industry-icons/Union.svg',
}

const userStats = ref<UserStat[]>([
  {
    type: 'total',
    title: '总用户数（人）',
    value: '1,245,678',
    icon: iconData.totalUser,
    unit: '人',
    compare: '+1.2%',
  },
  {
    type: 'pipeline',
    title: '活跃用户数（人）',
    value: '1,245,678',
    icon: iconData.inUser,
    unit: '人',
    compare: '+1.2%',
  },
])

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
  startTooltipAnimation()
}

// 开始 tooltip 轮播
const startTooltipAnimation = () => {
  const dataCount = xData.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }

  tooltipTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 2, // 使用主要的bar系列
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 显示新的 tooltip
    chart?.dispatchAction({
      type: 'showTip',
      seriesIndex: 2, // 在主要的bar系列上显示 tooltip
      dataIndex: currentIndex,
    })

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 2, // 使用主要的bar系列
      dataIndex: currentIndex,
    })
  }, 3000) // 每隔3秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (tooltipTimer) {
    clearInterval(tooltipTimer)
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .chart-content {
    flex: 1;
    padding: 4px;

    .chart-container {
      width: 100%;
      height: 100%;
    }
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 33.33%;
  flex-shrink: 0;
}

.user-card {
  position: relative;
  width: 100%;
  height: 104px;
  overflow: hidden;

  .card-content {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    height: 64px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    gap: 16px;

    .card-icon {
      flex-shrink: 0;
      width: 48px;
      height: 48px;
      display: flex;
      position: relative;
      align-items: center;
      justify-content: center;

      > img {
        position: absolute;
        top: 0;
        left: 0;
        width: 48px;
        height: 48px;
        z-index: 1;
      }

      .second-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        z-index: 2;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .card-info {
      flex: 1;
      height: 48px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;

      .card-title {
        width: 100%;
        height: 20px;
        white-space: nowrap;
        color: #66ffff;
        font-family: 'Noto Sans SC';
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        margin-bottom: 4px;
      }

      .card-value-container {
        width: 100%;
        height: 24px;
        display: flex;
        flex-direction: row;
        justify-content: start;
        align-items: baseline;

        .card-value {
          color: #ffffff;
          font-family: 'DINPro';
          font-size: 18px;
          line-height: 24px;
          font-weight: bold;
          margin-right: 4px;
        }

        .card-unit {
          color: #ffffff;
          font-family: 'Noto Sans SC';
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
  }

  .card-border-bottom {
    position: absolute;
    left: 30px;
    bottom: 0px;
    width: calc(100% - 60px);
    height: 12px;
  }

  .card-side-lines {
    .side-line {
      position: absolute;
      top: 0px;
      width: 5.5px;
      height: 115px;

      &.left {
        left: 0px;
      }

      &.right {
        right: 0px;
      }
    }
  }
}
.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}
.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
/* 卡片同比样式 */
.card-compare {
  margin-top: 2px;
  color: #fff;
  font-size: 10px;

  font-weight: lighter;
}
.card-compare-number {
  font-size: 11px;
  font-weight: 300;
  color: #fff;
  margin-left: 2px;
}
</style>
