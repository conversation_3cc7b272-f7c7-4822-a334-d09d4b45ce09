<template>
  <div class="menu-popover" v-show="show" ref="popoverRef">
    <div class="menu-list">
      <div
        v-for="menu in menus"
        :key="menu.path"
        class="cursor-pointer menu-item"
        :class="{ active: selectedPath === menu.path }"
        @click="handleMenuClick(menu)"
      >
        <div class="menu-text">{{ menu.name }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface MenuItem {
  name: string
  path: string
}

defineProps<{
  show: boolean
  menus: MenuItem[]
  selectedPath: string | null
}>()

const emit = defineEmits(['update:show', 'select'])

const handleMenuClick = (menu: MenuItem) => {
  emit('select', menu)
}
</script>

<style scoped>
.menu-popover {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 140px;
  z-index: 1000;
  border-radius: 8px;
  overflow: hidden;
  box-sizing: border-box;
}

.menu-popover::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  border-radius: 8px;
  background: linear-gradient(
    180deg,
    rgba(153, 213, 255, 0.3) 0%,
    rgba(153, 213, 255, 0) 0%,
    rgba(153, 213, 255, 0.5) 15%,
    rgba(153, 213, 255, 0.8) 100%
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

.menu-list {
  padding: 8px 0;
  position: relative;
  background: linear-gradient(180deg, rgba(11, 46, 115, 0.3) 0%, rgba(11, 46, 115, 0.6) 100%);
  backdrop-filter: blur(8px);
  border-radius: 8px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px;
  transition: all 0.3s;
}

.menu-item:hover {
  background: rgba(0, 146, 255, 0.6);
}

.menu-item.active {
  background: rgba(255, 217, 102, 0.8);
}

.menu-text {
  color: #fff;
  font-size: 14px;
}
</style>
