<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">特殊用户管理</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 左侧统计卡片区域 -->
        <div class="user-overview-container">
          <div v-for="(item, index) in userStats" :key="index" class="user-card">
            <img class="user-card-bg" src="/src/assets/indoor-icon/right-bg.svg" alt="bg" />
            <div class="card-content">
              <div class="card-icon">
                <img :src="item.icon" alt="" />
                <div v-if="item.secondIcon" class="second-icon">
                  <img :src="item.secondIcon" alt="" />
                </div>
              </div>
              <div class="card-info">
                <div class="card-title">{{ item.title }}</div>
                <div class="card-value-container">
                  <div class="card-value">{{ item.value }}</div>
                  <div class="card-unit">{{ item.unit }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧表格区域 -->
        <div class="alarm-table">
          <div class="table-header">
            <div class="header-cell">报警时间</div>
            <div class="header-cell">报警设备</div>
            <div class="header-cell">设备位置</div>
            <div class="header-cell">处置状态</div>
          </div>
          <div class="table-body">
            <ul class="alarm-list">
              <li
                v-for="(item, index) in alarmList"
                :key="index"
                class="alarm-item"
                :class="{ highlighted: item.highlighted }"
              >
                <span class="alarm-cell">{{ item.alarmTime }}</span>
                <span class="alarm-cell">{{ item.alarmDevice1 }}</span>
                <span class="alarm-cell">{{ item.alarmDevice2 }}</span>
                <span class="alarm-cell status-cell" :class="getStatusClass(item.status)">{{ item.status }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 定义用户统计数据接口
interface UserStat {
  type: string
  title: string
  value: string
  icon: string
  secondIcon?: string // 添加可选的 secondIcon 属性
  unit?: string // 添加可选的 unit 属性
}

// 定义报警数据接口
interface AlarmItem {
  alarmTime: string
  alarmDevice1: string
  alarmDevice2: string
  status: string
  highlighted?: boolean
}

// 图标数据配置 - 使用图片路径
const iconData = {
  userDone: '/src/assets/indoor-icon/user-done.svg',
  userUndone: '/src/assets/indoor-icon/user-undone.svg',
  iconDown: '/src/assets/indoor-icon/user-bg.svg',
  union: '/src/assets/industry-icons/Union.svg',
}

const userStats = ref<UserStat[]>([
  {
    type: 'total',
    title: '已管控',
    value: '1,245,678',
    icon: iconData.userDone,
    unit: '户',
  },
  {
    type: 'pipeline',
    title: '未管控',
    value: '1,245,678',
    icon: iconData.userUndone,
    unit: '户',
  },
])

// 报警列表数据
const alarmList = ref<AlarmItem[]>([
  {
    alarmTime: '2025-09-04 08:30:12',
    alarmDevice1: '燃气检测器',
    alarmDevice2: '一号楼A区',
    status: '待处置',
    highlighted: true,
  },
  {
    alarmTime: '2025-09-04 08:25:45',
    alarmDevice1: '压力传感器',
    alarmDevice2: '二号楼B区',
    status: '处理中',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 08:20:33',
    alarmDevice1: '温度传感器',
    alarmDevice2: '三号楼C区',
    status: '已处置',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 08:15:28',
    alarmDevice1: '流量监测器',
    alarmDevice2: '四号楼D区',
    status: '待处置',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 08:10:15',
    alarmDevice1: '阀门控制器',
    alarmDevice2: '五号楼E区',
    status: '已处置',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 08:05:42',
    alarmDevice1: '燃气检测器',
    alarmDevice2: '六号楼F区',
    status: '处理中',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 08:00:18',
    alarmDevice1: '压力传感器',
    alarmDevice2: '七号楼G区',
    status: '待处置',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 07:55:23',
    alarmDevice1: '温度传感器',
    alarmDevice2: '八号楼H区',
    status: '已处置',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 07:50:37',
    alarmDevice1: '流量监测器',
    alarmDevice2: '九号楼I区',
    status: '处理中',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 07:45:51',
    alarmDevice1: '阀门控制器',
    alarmDevice2: '十号楼J区',
    status: '待处置',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 07:40:29',
    alarmDevice1: '燃气检测器',
    alarmDevice2: '十一号楼K区',
    status: '已处置',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 07:35:14',
    alarmDevice1: '压力传感器',
    alarmDevice2: '十二号楼L区',
    status: '处理中',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 07:30:46',
    alarmDevice1: '温度传感器',
    alarmDevice2: '十三号楼M区',
    status: '待处置',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 07:25:32',
    alarmDevice1: '流量监测器',
    alarmDevice2: '十四号楼N区',
    status: '已处置',
    highlighted: false,
  },
  {
    alarmTime: '2025-09-04 07:20:58',
    alarmDevice1: '阀门控制器',
    alarmDevice2: '十五号楼O区',
    status: '处理中',
    highlighted: false,
  },
])

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case '待处置':
      return 'status-pending'
    case '处理中':
      return 'status-processing'
    case '已处置':
      return 'status-completed'
    default:
      return ''
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 16px;
}

.stats-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8px;
  width: 248px;
}

.stat-card {
  position: relative;
  width: 120px;
  height: 108px;
  background: linear-gradient(0deg, rgba(64, 159, 255, 0.15) 0%, rgba(64, 159, 255, 0) 100%);
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, rgba(64, 159, 255, 0.15) 100%) 1;

  .card-title {
    position: absolute;
    bottom: 12px;
    left: 18px;
    width: 84px;
    height: 20px;
    white-space: nowrap;
    color: #99d5ff;
    font-family: 'Noto Sans SC';
    font-size: 14px;
    line-height: 20px;
    text-align: center;
  }

  .card-icon {
    position: absolute;
    top: 32px;
    left: 28px;
    width: 64px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    .point-icon {
      width: 60px;
      height: 55px;
    }
  }

  .card-value {
    position: absolute;
    top: 12px;
    left: 40px;
    width: 40px;
    height: 32px;
    white-space: nowrap;
    color: #ffc61a;
    font-family: 'DINPro';
    font-size: 24px;
    line-height: 32px;
    text-align: center;
  }
}

.alarm-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .table-header {
    display: flex;
    height: 48px;
    background: linear-gradient(270deg, rgba(64, 159, 255, 0.3) 0%, rgba(64, 159, 255, 0) 100%);
    flex-shrink: 0;

    .header-cell {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 16px;
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 14px;
      line-height: 20px;
    }
  }

  .table-body {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(64, 159, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(64, 159, 255, 0.5);
      border-radius: 3px;

      &:hover {
        background: rgba(64, 159, 255, 0.7);
      }
    }

    /* Firefox 滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(64, 159, 255, 0.5) rgba(64, 159, 255, 0.1);
  }

  .alarm-list {
    list-style: none;
    margin: 0;
    padding: 0;

    .alarm-item {
      display: flex;
      height: 48px;
      border-bottom: 1px solid rgba(64, 159, 255, 0.3);

      &.highlighted {
        background: rgba(64, 159, 255, 0.15);
      }

      &:last-child {
        border-bottom: none;
      }

      .alarm-cell {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 16px;
        color: rgba(255, 255, 255, 0.8);
        font-family: 'Noto Sans SC';
        font-size: 14px;
        line-height: 20px;

        &.status-cell {
          font-weight: 500;

          &.status-pending {
            color: #ff6b6b;
          }

          &.status-processing {
            color: #ffc61a;
          }

          &.status-completed {
            color: #51cf66;
          }
        }
      }
    }
  }
}
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 50%;
  min-width: 420px;
}

.chart-content {
  flex: 1;
  padding: 4px;

  .chart-container {
    width: 100%;
    height: 100%;
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  width: 33.33%;
  flex-shrink: 0;
  height: 100%;
}

.user-card {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
}

.user-card-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
  pointer-events: none;
}

.user-card:not(:last-child) {
  margin-bottom: 16px;
}

.card-content {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  bottom: 12px;
  height: auto;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}

.card-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;

  > img {
    position: absolute;
    top: 0;
    left: 0;
    width: 48px;
    height: 48px;
    z-index: 1;
  }

  .second-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    z-index: 2;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-info {
  flex: 1;
  height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  .card-title {
    width: 100%;
    height: 20px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC';
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    margin-bottom: 4px;
  }

  .card-value-container {
    width: 100%;
    height: 24px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: baseline;

    .card-value {
      color: #ffffff;
      font-family: 'DINPro';
      font-size: 18px;
      line-height: 24px;
      font-weight: bold;
      margin-right: 4px;
    }

    .card-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.card-border-bottom {
  position: absolute;
  left: 30px;
  bottom: 0px;
  width: calc(100% - 60px);
  height: 12px;
}
</style>
