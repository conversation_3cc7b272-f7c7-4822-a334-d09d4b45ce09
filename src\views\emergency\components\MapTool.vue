<script setup lang="ts">
const emit = defineEmits<{
  (e: 'click', value: boolean): void
}>()

const handleShowDialog = () => {
  // 这里可以触发测算预演的弹窗显示逻辑
  console.log('测算预演 clicked')
  emit('click', true)
}
</script>
<template>
  <div class="w-[148px] p-4 layers-wrapper">
    <div class="flex flex-col gap-2">
      <div class="layer-group">
        <!-- <div class="flex items-center space-x-2"> -->
        <div class="flex items-center gap-2 text-sm font-medium text-[#99D5FF]">
          <div class="checkbox-icon toolbox"></div>
          <span>工具箱</span>
        </div>
        <!-- </div> -->
        <div class="pl-6 mt-2 space-y-2">
          <div class="flex items-center space-x-2" @click="handleShowDialog">
            <span class="text-sm leading-none cursor-pointer select-none text-[#99D5FF]">测算预演</span>
          </div>
        </div>
      </div>
    </div>
    <div class="layers-wrapper-bottom"></div>
  </div>
</template>
<style scoped>
@import '@/styles/index.css';
.checkbox-icon {
  width: 16px;
  height: 16px;
  background-size: 100% 100%;

  &.toolbox {
    background: url('@/assets/emergency/tool-box-icon.png') no-repeat center;
  }
}
.layer-group {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
  color: #99d5ff;
}

.layer-group:last-child {
  border-bottom: none;
}
</style>
