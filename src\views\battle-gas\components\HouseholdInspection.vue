<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">气瓶年限分布</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 右侧图表区域 -->
        <div class="chart-panel">
          <div class="chart-content">
            <div ref="chartRef" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

// Mock数据

let chartdata = [
  [111, 222, 333],
  ['石油', '煤矿', '汽油'],
]

var getmydmc = chartdata[1] //数据点名称
var getmyd = chartdata[0] //收入金额
var getmydzd = []

let big = 0
getmyd.forEach(el => {
  if (!(el === undefined || el === '')) {
    if (big < Number(el)) {
      big = Number(el)
    }
  }
})
for (let i = 0; i < getmyd.length; i++) {
  getmydzd.push(big * 4)
}
//计算最大值
function calMax(arr: any) {
  let max = 0
  arr.forEach((el: any) => {
    el.forEach((el1: any) => {
      if (!(el1 === undefined || el1 === '')) {
        if (max < Number(el1)) {
          max = Number(el1)
        }
      }
    })
  })
  let maxint = Math.ceil(max / 9.5)
  //不让最高的值超过最上面的刻度
  let maxval = maxint * 10
  //让显示的刻度是整数
  return maxval
}

var max = Math.ceil(calMax([getmyd]) / 10) * 10
const option = {
  backgroundColor: 'transparent',
  grid: {
    left: '10%',
    right: '3%',
    bottom: '-8%',
    top: '1%',
    containLabel: true,
  },
  tooltip: {
    formatter: (params: any) => {
      if (!params.name) return ''
      // 千分位格式化
      const formatNumber = (n: any) => (n == null ? '-' : n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','))
      const value = getmyd[params.dataIndex]
      return `
        <div style="min-width:120px;padding:6px 12px 6px 8px;">
          <div style="font-size:14px;color:#fff;font-weight:bold;">${params.name}</div>
          <div style="margin-top:2px;font-size:13px;color:#ffd700;">
            <span style="font-weight:500;">${formatNumber(value)}</span>
            <span style="color:#b2e3ff;font-size:12px;margin-left:4px;">单位</span>
          </div>
        </div>
      `
    },
    backgroundColor: 'rgba(0,32,64,0.95)',
    borderColor: '#409fff',
    borderWidth: 1,
    extraCssText: 'box-shadow:0 2px 12px rgba(0,0,0,0.3);border-radius:6px;',
    textStyle: {
      align: 'left',
      fontFamily: 'Noto Sans SC',
    },
  },
  xAxis: [
    {
      type: 'value',
      axisLabel: {
        show: false,
      },
      min: 0,
      max: max, // 计算最大值
      interval: max / 5, //  平均分为5份
      splitNumber: 5,
      splitLine: {
        show: false,
        lineStyle: {
          color: 'transparent',
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'transparent',
          width: 1,
          opacity: 0.3,
        },
      },
      axisTick: {
        show: false,
      },
    },
    {
      type: 'value',
      axisLabel: {
        show: false,
      },
      min: 0,
      max: max, // 计算最大值
      interval: max / 10, //  平均分为5份
      splitNumber: 10,
      splitLine: {
        show: false,
        lineStyle: {
          type: 'dashed',
          color: 'transparent',
        },
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: 'transparent',
        },
      },
      axisTick: {
        show: false,
      },
    },
  ],
  yAxis: [
    {
      type: 'category',
      inverse: true,
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: 'transparent',
          width: 1,
          opacity: 0.3,
        },
      },
      data: getmydmc,
    },
  ],
  dataZoom: [
    {
      type: 'inside',
      show: true,
      height: 15,
      start: 1,
      end: 100,
      orient: 'vertical',
      zlevel: 66,
    },
  ],
  series: [
    {
      name: '值',
      type: 'bar',
      xAxisIndex: 0,
      itemStyle: {
        normal: {
          color: {
            colorStops: [
              {
                offset: 0,
                color: 'rgba(1, 255, 255, 1)', // 渐变起始色
              },
              {
                offset: 1,
                color: 'rgba(25, 159, 255, 1)', // 渐变结束色
              },
            ],
          },
        },
      },
      barWidth: 15,
      barCategoryGap: '40%', // 增加bar之间的间距
      data: getmyd,
      z: 0,
      label: {
        show: true,
        position: [580, -22], // 百分比label位置
        formatter: function (params: any) {
          let percent = max ? Math.round((params.value / max) * 100) : 0
          return percent + '%'
        },
        color: '#66FFFF',
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    {
      // 图标图片
      type: 'pictorialBar',
      symbol: 'image://src/assets/battle-gas/bar-icon.svg',
      symbolSize: [40, 40],
      symbolPosition: 'start',
      symbolOffset: [-52, -10],
      data: getmyd,
      z: 100,
      tooltip: { show: false },
    },
    {
      // 图标图片
      type: 'pictorialBar',
      symbol: 'image://src/assets/battle-gas/bar-bg.svg',
      symbolSize: [690, 60],
      symbolPosition: 'start',
      symbolOffset: [-12, -12],
      data: getmyd,
      z: 1,
      tooltip: { show: false },
    },
    {
      // 分隔+名称label
      type: 'pictorialBar',
      itemStyle: {
        normal: {
          color: '#022539',
        },
      },
      symbolRepeat: 'fixed',
      symbolMargin: 4,
      symbol: 'rect',
      symbolClip: true,
      symbolSize: [2, 15],
      symbolPosition: 'start',
      symbolOffset: [-1, 0],
      data: getmydzd,
      z: 66,
      animationEasing: 'elasticOut',
      label: {
        show: true,
        position: [0, 4], // 名称label位置
        formatter: function (params: any) {
          return getmydmc[params.dataIndex]
        },
        color: '#FFFF',
        fontSize: 12,
        fontWeight: 'lighter',
        align: 'left',
      },
    },
    {
      name: '背景',
      type: 'bar',
      barWidth: 15,
      barGap: '-100%',
      xAxisIndex: 1,
      data: getmydzd,
      itemStyle: {
        normal: {
          color: {
            colorStops: [
              {
                offset: 0,
                color: 'rgba(247, 251, 255, 0.1991)',
              },
              {
                offset: 1,
                color: 'rgba(247, 251, 255, 0.1991)',
              },
            ],
          },
        },
      },
      z: 0,
    },
    {
      // 外边框
      type: 'pictorialBar',
      symbol: 'rect',
      itemStyle: {
        normal: {
          color: 'none',
        },
      },
      label: {
        normal: {
          formatter: (params: any) => {
            var text
            text = '{f|  ' + params.data + '}'
            return text
          },
          rich: {
            f: {
              color: '#66FFFF',
              fontSize: '16',
              padding: [0, 0, 0, 0], // 调整padding为0
              fontWeight: 'bold',
            },
          },
          position: [520, 3],
          padding: [0, 0, 0, 0],
          show: true,
        },
      },
      data: getmyd,
      z: 77,
      animationEasing: 'elasticOut',
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .chart-content {
    flex: 1;
    padding: 2px;

    .chart-container {
      width: 100%;
      height: 100%;
    }
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 33.33%;
  flex-shrink: 0;
}

.user-card {
  position: relative;
  width: 100%;
  height: 104px;
  overflow: hidden;

  .card-content {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    height: 64px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    gap: 16px;

    .card-icon {
      flex-shrink: 0;
      width: 48px;
      height: 48px;
      display: flex;
      position: relative;
      align-items: center;
      justify-content: center;

      > img {
        position: absolute;
        top: 0;
        left: 0;
        width: 48px;
        height: 48px;
        z-index: 1;
      }

      .second-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        z-index: 2;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .card-info {
      flex: 1;
      height: 48px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;

      .card-title {
        width: 100%;
        height: 20px;
        white-space: nowrap;
        color: #66ffff;
        font-family: 'Noto Sans SC';
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        margin-bottom: 4px;
      }

      .card-value-container {
        width: 100%;
        height: 24px;
        display: flex;
        flex-direction: row;
        justify-content: start;
        align-items: baseline;

        .card-value {
          color: #ffffff;
          font-family: 'DINPro';
          font-size: 18px;
          line-height: 24px;
          font-weight: bold;
          margin-right: 4px;
        }

        .card-unit {
          color: #ffffff;
          font-family: 'Noto Sans SC';
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
  }

  .card-border-bottom {
    position: absolute;
    left: 30px;
    bottom: 0px;
    width: calc(100% - 60px);
    height: 12px;
  }

  .card-side-lines {
    .side-line {
      position: absolute;
      top: 0px;
      width: 5.5px;
      height: 115px;

      &.left {
        left: 0px;
      }

      &.right {
        right: 0px;
      }
    }
  }
}
.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}
.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
</style>
