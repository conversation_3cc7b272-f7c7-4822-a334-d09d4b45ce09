<template>
  <div class="panel-container-col">
    <div class="panel-header">车辆概况</div>
    <div class="p-4 panel-content">
      <div class="grid grid-cols-2 gap-4">
        <div class="indicator" v-for="item in cards" :key="item.key">
          <div class="indicator-left">
            <div class="title">{{ item.title }}</div>
            <div class="sub">车辆总数</div>
            <div class="value">
              <span class="num">{{ item.value }}</span>
              <span class="unit">辆</span>
            </div>
          </div>
          <div class="indicator-right">
            <div class="ring-box">
              <div class="">
                <div class="ring-outer"></div>
                <div class="ring-track"></div>
                <div class="ring-fill" :style="{ ['--p' as any]: item.percent }">
                  <!-- <span class="sep"></span> -->
                </div>
                <div class="ring-center">
                  <span class="percent">{{ item.percent }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const cards = ref([
  { key: 'oil', title: '液化石油气配送车辆', value: 58, percent: 30 },
  { key: 'bottle', title: '瓶装气运输车', value: 58, percent: 50 },
  { key: 'inspect', title: '巡检车辆', value: 58, percent: 50 },
  { key: 'chem', title: '危险化学品运输车', value: 58, percent: 50 },
  { key: 'lng', title: '液化天然气运输车', value: 58, percent: 50 },
  { key: 'other', title: '其它车辆', value: 58, percent: 50 },
])
</script>

<style scoped>
@import '@/styles/index.css';

.indicator {
  width: 348px;
  height: 119px;
  background: linear-gradient(
    270deg,
    rgba(64, 159, 255, 0) 0%,
    rgba(64, 159, 255, 0.15) 50%,
    rgba(64, 159, 255, 0) 100%
  );
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(0deg, rgba(64, 159, 255, 0) 0%, rgba(64, 159, 255, 0.6) 49%, rgba(64, 159, 255, 0) 100%)
    1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.indicator-left .title {
  font-size: 16px;
  color: #fff;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 12px;
}

.indicator-left .sub {
  color: #fff;
  font-size: 14px;
}

.indicator-left .value .num {
  font-size: 24px;
  font-weight: 700;
  color: #8cffff;
}

.indicator-left .value .unit {
  color: #8cffff;
  margin-left: 6px;
}

.indicator-right {
  width: 160px;
  height: 96px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ring-box {
  position: relative;
  width: 96px;
  height: 96px;
}

/* .ring-dash {
  position: absolute;
  inset: -12px;
  border: 2px dashed rgba(153, 213, 255, .5);
  border-radius: 8px;
} */

/* 双环：外环容器 + 内环进度条 */
.ring {
  position: relative;
  width: 96px;
  height: 96px;
  border: none;
}
.ring-outer {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  box-shadow: inset 0 0 0 20px rgba(64, 159, 255, 0.25);
}
.ring-track {
  position: absolute;
  inset: 5px;
  border-radius: 50%;
  box-shadow: inset 0 0 0 10px rgba(64, 159, 255, 0.22);
  background: transparent;
}
.ring-fill {
  --p: 50;
  position: absolute;
  inset: 5px;
  border-radius: 60%;
  background: conic-gradient(
    rgba(115, 250, 234, 0) 0,
    rgba(115, 250, 234, 0.75) calc(var(--p) * 0.6%),
    rgba(115, 250, 234, 1) calc(var(--p) * 1%),
    transparent calc(var(--p) * 1%)
  );
  -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 10px), #000 calc(100% - 10px));
  mask: radial-gradient(farthest-side, transparent calc(100% - 10px), #000 calc(100% - 10px));
}
.ring-fill .sep {
  position: absolute;
  right: 0;
  top: -6px;
  width: 8px;
  height: calc(100% + 12px);
  background: #73faea;
  border-left: 2px solid rgba(64, 159, 255, 0.35);
  border-radius: 2px;
}
.ring-center {
  position: absolute;
  inset: 20px;
  border-radius: 50%;
  background: #0f2a52;
  display: grid;
  place-items: center;
}
.percent {
  color: #8cffff;
  font-size: 24px;
  font-weight: 700;
}
</style>
