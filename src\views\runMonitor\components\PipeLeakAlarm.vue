<template>
  <div class="panel-container">
    <div class="panel-header">管道泄漏告警</div>
    <div class="p-4 panel-content">
      <Table class="w-full table-fixed">
        <colgroup>
          <col style="width: 40%" />
          <col style="width: auto" />
          <col style="width: 20%" />
        </colgroup>
        <TableHeader>
          <TableRow class="border-[#99D5FF]/30 hover:bg-transparent">
            <TableHead class="font-bold text-white">管道编号</TableHead>
            <TableHead class="font-bold text-white">告警时间</TableHead>
            <TableHead class="font-bold text-white">操作</TableHead>
          </TableRow>
        </TableHeader>
      </Table>
      <div class="overflow-hidden" :style="{ height: `${4 * rowHeight}px` }" ref="tableContainerRef">
        <Table class="w-full table-fixed">
          <colgroup>
            <col style="width: 40%" />
            <col style="width: auto" />
            <col style="width: 20%" />
          </colgroup>
          <TableBody
            :style="{
              transform: `translateY(-${scrollTop}px)`,
              transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
            }"
          >
            <TableRow
              v-for="(item, index) in scrollList"
              :key="index"
              class="border-[#99D5FF]/30 hover:bg-[#99D5FF]/30"
              :style="{ height: `${rowHeight}px` }"
            >
              <TableCell class="overflow-hidden whitespace-nowrap text-ellipsis">{{ item.id }}</TableCell>
              <TableCell>{{ item.time }}</TableCell>
              <TableCell class="text-[#99D5FF] cursor-pointer" @click="handleShowDialog(item)">测算</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  Table,
  TableBody,
  // TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

const handleShowDialog = (record: any) => {
  emit('click', record)
}

const sourceData = ref([
  {
    id: 'plpeLineId-5975',
    time: '2025-07-02 15:24:50',
    duration: '30分钟',
    position: { lng: 115.915, lat: 39.055 },
    device: '管网泄漏探测器-01',
  },
  {
    id: 'plpeLineId-2387',
    time: '2025-02-21 14:42:44',
    duration: '15分钟',
    position: { lng: 115.92, lat: 39.06 },
    device: '管网泄漏探测器-02',
  },
  {
    id: 'plpeLineId-3578',
    time: '2024-11-14 15:22:34',
    duration: '45分钟',
    position: { lng: 115.91, lat: 39.05 },
    device: '管网泄漏探测器-03',
  },
  {
    id: 'plpeLineId-3563',
    time: '2024-11-08 16:40:49',
    duration: '60分钟',
    position: { lng: 115.925, lat: 39.065 },
    device: '管网泄漏探测器-04',
  },
  {
    id: 'plpeLineId-3967',
    time: '2024-11-09 15:22:49',
    duration: '60分钟',
    position: { lng: 115.925, lat: 39.065 },
    device: '管网泄漏探测器-05',
  },
])

const scrollList = computed(() => [...sourceData.value, ...sourceData.value])
const tableContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const rowHeight = 48 // 每行高度为40px
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight

    if (scrollTop.value >= sourceData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 3000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  startScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})

onUnmounted(() => {
  stopScrolling()
  const container = tableContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<style scoped>
@import '@/styles/index.css';
</style>
