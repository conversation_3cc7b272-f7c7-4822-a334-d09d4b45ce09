<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">场站监控画面</div>
    </div>
    <div class="flex p-4 panel-content">
      <div class="pr-4 grow">
        <div class="header-dropdown">
          <Combobox by="label" class="">
            <ComboboxAnchor class="w-full dropdown-btn">
              <div class="relative items-center w-full">
                <ComboboxInput class="" :display-value="val => val?.label ?? ''" placeholder="请输入监控画面查询点位" />
                <span class="absolute inset-y-0 flex items-center justify-center px-3 start-0">
                  <Search class="size-4 text-muted-foreground" />
                </span>
              </div>
            </ComboboxAnchor>

            <ComboboxList class="w-[296px] bg-[#409fff]/15 border-none">
              <ComboboxEmpty>No framework found.</ComboboxEmpty>

              <ComboboxGroup>
                <ComboboxItem
                  v-for="framework in options"
                  :key="framework.value"
                  :value="framework"
                  class="text-[#409fff]"
                >
                  {{ framework.label }}

                  <ComboboxItemIndicator>
                    <Check :class="cn('ml-auto h-4 w-4')" />
                  </ComboboxItemIndicator>
                </ComboboxItem>
              </ComboboxGroup>
            </ComboboxList>
          </Combobox>
        </div>
        <ul class="flex flex-wrap gap-2 py-4">
          <li
            v-for="video in videoList"
            :key="video.id"
            @click="handleChangeVideo(video)"
            :class="[
              'w-[144px] h-8 leading-8 text-center text-sm border border-[#409fff] bg-[#409fff]/15 rounded-xs cursor-pointer',
              { 'border-[#FFC61A] bg-[#FFC61A]/15': video.id === videoInfo.id },
            ]"
          >
            {{ video.title }}
          </li>
        </ul>
      </div>
      <div class="relative w-[400px] h-[224px] overflow-hidden rounded" @click="handleShowDialog(videoInfo)">
        <video
          class="object-contain w-full h-full"
          :src="videoInfo.url"
          disablepictureinpicture
          muted
          loop
          autoplay
          playsinline
        ></video>
        <div class="absolute bottom-0 flex items-center video-label flex-start">
          <MapPin class="w-4 h-4 ml-3" />
          <div class="ml-1 text-xs">{{ videoInfo.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue'
import { MapPin, Search, Check } from 'lucide-vue-next'
import { cn } from '@/lib/utils'
import {
  Combobox,
  ComboboxAnchor,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxInput,
  ComboboxItem,
  ComboboxItemIndicator,
  ComboboxList,
} from '@/components/ui/combobox'

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

// 站点选项数据
const options = ref([
  { label: '华润燃气东门路口', value: '1' },
  { label: '兴达街路口东北角', value: '2' },
  { label: '中燃能源调压站正门', value: '3' },
])

const videoList = [
  {
    id: '1',
    title: '华润燃气东门路口',
    address: '东门路口',
    url: '/mock/stream.webm',
  },
  {
    id: '2',
    title: '兴达街路口东北角',
    address: '兴达街路口东北角',
    url: '/mock/stream2.webm',
  },
  {
    id: '3',
    title: '中燃能源调压站正门',
    address: '中燃能源调压站正门',
    url: '/mock/stream3.webm',
  },
]

// 监听选中值变化，更新视频信息
const videoInfo = ref<any>(
  videoList.find(v => v.id === '1') || {
    id: '1',
    title: '华润燃气东门路口',
    address: '东门路口',
    url: '/mock/stream.webm',
  },
)

// 监听选中值变化
watch(videoInfo, newValue => {
  const selectedVideo = videoList.find(v => v.id === newValue.id)
  if (selectedVideo) {
    videoInfo.value = selectedVideo
  }
})

const handleChangeVideo = (video: any) => {
  videoInfo.value = video
}

const handleShowDialog = (video: any) => {
  emit('click', video)
}
</script>

<style scoped>
@import '@/styles/index.css';

.dropdown-btn {
  border: 1px solid #409fff;
  color: #409fff;
  border-radius: 4px;
  background: rgba(64, 159, 255, 0.15);
  box-sizing: border-box;
  font-size: 12px;
  height: auto;
  outline: none;
}

.video-label {
  width: 448px;
  height: 24px;
  background: rgba(16, 34, 54, 0.6);
}
</style>
