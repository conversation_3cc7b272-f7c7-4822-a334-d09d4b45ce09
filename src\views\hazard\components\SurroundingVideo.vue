<template>
  <div class="panel-container">
    <div class="panel-header">重点防控点位周边监控</div>
    <div class="flex flex-col gap-4 p-4 panel-content">
      <div class="h-full" v-for="videoInfo in videoList" :key="videoInfo.id">
        <div class="relative w-full h-full overflow-hidden rounded" @click="handleShowDialog(videoInfo)">
          <video
            class="object-contain w-full h-full"
            :src="videoInfo.url"
            disablepictureinpicture
            muted
            loop
            autoplay
            playsinline
          ></video>
          <div class="absolute bottom-0 flex items-center video-label flex-start">
            <MapPin class="w-4 h-4 ml-3" />
            <div class="ml-1 text-xs">{{ videoInfo.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { MapPin } from 'lucide-vue-next'

const emit = defineEmits<{
  (e: 'click', value: any): void
}>()

const videoList = ref([
  {
    id: '1',
    title: '华润燃气东门路口',
    address: '东门路口',
    url: '/mock/stream.webm',
  },
  // {
  //   id: '2',
  //   title: '兴达街路口东北角',
  //   address: '兴达街路口东北角',
  //   url: '/mock/stream2.webm',
  // },
])

const handleShowDialog = (video: any) => {
  emit('click', video)
}
</script>

<style scoped>
@import '@/styles/index.css';
.video-label {
  width: 448px;
  height: 24px;
  background: rgba(16, 34, 54, 0.6);
}
</style>
