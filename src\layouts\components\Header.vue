<template>
  <div class="header-wrapper">
    <div class="flex items-center justify-between header-content">
      <div class="flex items-center gap-2.5">
        <div class="location-icon"></div>
        <div class="info-text">肥乡区 | {{ currentTime }} | {{ currentDate }} | {{ currentDay }}</div>
      </div>
      <div class="flex items-center gap-7.5">
        <div class="relative">
          <div class="cursor-pointer menus-icon" @mouseenter="showMenus = true" @mouseleave="handleMouseLeave"></div>
          <MenuPopover
            v-model:show="showMenus"
            :menus="menuItems"
            :selected-path="isFixedPath ? null : currentPath"
            @select="handleMenuSelect"
            @mouseenter="showMenus = true"
            @mouseleave="handleMouseLeave"
          />
        </div>
        <div class="settings-icon"></div>
        <div class="info-text">
          {{ weatherInfo.temperature }}℃ | {{ weatherInfo.weather }} | {{ weatherInfo.winddirection }}风 |
          {{ weatherInfo.windpower }}级
        </div>
      </div>
    </div>
    <div class="nav-tabs">
      <div class="nav-item" @click="navigateTo('/gas')" :class="{ active: currentPath === '/gas' }">
        <div class="nav-item-text">智慧燃气</div>
      </div>
      <div
        class="nav-item"
        @click="navigateTo('/situation-monitor')"
        :class="{ active: currentPath === '/situation-monitor' }"
      >
        <div class="nav-item-text">态势监管</div>
      </div>
      <!-- <div class="nav-item" @click="navigateTo('/run-monitor')" :class="{ active: currentPath === '/run-monitor' }">
        <div class="nav-item-text">运行监测</div>
      </div> -->
      <!-- <div
        class="nav-item"
        @click="navigateTo('/patrol-inspection')"
        :class="{ active: currentPath === '/patrol-inspection' }"
      >
        <div class="nav-item-text">巡查巡检</div>
      </div>
      <div class="nav-item" @click="navigateTo('/battle-gas')" :class="{ active: currentPath === '/battle-gas' }">
        <div class="nav-item-text">瓶装石油气</div>
      </div>
      <div class="nav-item" @click="navigateTo('/indoor-check')" :class="{ active: currentPath === '/indoor-check' }">
        <div class="nav-item-text">入户安检</div>
      </div> -->
      <!-- 标题名称占位 -->
      <div class="title-placeholder">肥乡区城乡燃气监管信息平台</div>
      <div
        class="transform nav-item -scale-x-100"
        @click="navigateTo('/run-monitor')"
        :class="{ active: currentPath === '/run-monitor' }"
      >
        <div class="transform nav-item-text -scale-x-100">运行监测</div>
      </div>
      <div
        class="transform nav-item -scale-x-100"
        @click="isFixedPath && navigateTo(selectedMenuItem?.path || '')"
        :class="{ active: !isFixedPath }"
      >
        <div class="transform nav-item-text -scale-x-100">{{ selectedMenuItem?.name || '' }}</div>
      </div>
      <!-- <div
        class="transform nav-item -scale-x-100"
        @click="navigateTo('/transport')"
        :class="{ active: currentPath === '/transport' }"
      >
        <div class="transform nav-item-text -scale-x-100">运输车辆</div>
      </div>
      <div
        class="transform nav-item -scale-x-100"
        @click="navigateTo('/double-prevention')"
        :class="{ active: currentPath === '/double-prevention' }"
      >
        <div class="transform nav-item-text -scale-x-100">双重预防</div>
      </div>
      <div class="transform nav-item -scale-x-100">
        <div class="transform nav-item-text -scale-x-100">投诉监管</div>
      </div>
      <div
        class="transform nav-item -scale-x-100"
        @click="navigateTo('/evaluate')"
        :class="{ active: currentPath === '/evaluate' }"
      >
        <div class="transform nav-item-text -scale-x-100">安全评价</div>
      </div>
      <div
        class="transform nav-item -scale-x-100"
        @click="navigateTo('/hazard')"
        :class="{ active: currentPath === '/hazard' }"
      >
        <div class="transform nav-item-text -scale-x-100">重点防控</div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MenuPopover from '@/components/MenuPopover.vue'

const route = useRoute()
const router = useRouter()
const currentPath = ref(route.path)

// 判断是否是固定路径（不需要显示选中状态的路径）
const isFixedPath = computed(() => {
  return ['/gas', '/situation-monitor', '/run-monitor', '/404'].includes(currentPath.value)
})

// 菜单相关状态
const showMenus = ref(false)
const menuItems = ref([
  { name: '应急管理', path: '/emergency' },
  { name: '巡查巡检', path: '/patrol-inspection' },
  { name: '入户安检', path: '/indoor-check' },
  { name: '瓶装石油气', path: '/battle-gas' },
  { name: '运输车辆', path: '/transport' },
  { name: '双重预防', path: '/double-prevention' },
  { name: '投诉监管', path: '/complaint' },
  { name: '安全评价', path: '/evaluate' },
  { name: '重点防控', path: '/hazard' },
])

const currentTime = ref('')
const currentDate = ref('')
const currentDay = ref('')

const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']

const weatherInfo = ref({
  province: '-',
  city: '-',
  adcode: '-',
  weather: '-',
  temperature: '-',
  winddirection: '-',
  windpower: '-',
  humidity: '-',
  reporttime: '-',
  temperature_float: '-',
  humidity_float: '-',
})

// 根据当前路径获取选中的菜单项
const selectedMenuItem = computed(() => {
  if (isFixedPath.value) {
    return menuItems.value[0]
  }
  return menuItems.value.find(item => item.path === currentPath.value) || menuItems.value[0]
})

const handleMouseLeave = () => {
  showMenus.value = false
}

const handleMenuSelect = (menu: any) => {
  showMenus.value = false
  navigateTo(menu.path)
}

watch(
  () => route.path,
  newPath => {
    currentPath.value = newPath
  },
  { immediate: true },
)

const navigateTo = (path: string) => {
  router.push({ path })
}

const updateDateTime = () => {
  const now = new Date()

  // 更新时间
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  currentTime.value = `${hours}:${minutes}:${seconds}`

  // 更新日期
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const date = String(now.getDate()).padStart(2, '0')
  currentDate.value = `${year}.${month}.${date}`

  // 更新星期
  currentDay.value = weekDays[now.getDay()]
}

const fetchWeatherData = () => {
  fetch('https://restapi.amap.com/v3/weather/weatherInfo?key=a964a2cf3abcdb3e7b62a1e7786afc55&city=130407', {
    method: 'GET',
  })
    .then(async res => {
      const data = await res.json()
      weatherInfo.value = data.lives[0]
    })
    .catch(err => {
      console.error(err)
    })
}

onMounted(() => {
  fetchWeatherData()
  updateDateTime() // 立即更新一次时间
  // 设置定时器，每秒更新一次时间
  setInterval(updateDateTime, 1000)
})
</script>

<style scoped>
.header-wrapper {
  width: 100%;
  min-width: 2464px;
  height: 72px;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  background: url('@/assets/header/header-bg.png') no-repeat center;
  background-size: 2464px 72px;
  backdrop-filter: blur(4px);
}

.header-content {
  position: relative;
  height: 100%;
  padding: 0 24px;
}

.nav-tabs {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(690px + 140px * 4);
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.nav-item {
  position: relative;
  width: 140px;
  height: 36px;
  font-family: Noto Sans SC;
  font-size: 16px;
  line-height: 36px;
  text-align: center;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  background: url('@/assets/header/page-btn.png') no-repeat center;
  background-size: 100% 100%;
}

.nav-item:hover {
  filter: brightness(1.2);
  color: white;
}

.nav-item.active {
  background: url('@/assets/header/page-btn-active.png') no-repeat center;
  background-size: 100% 100%;
}

.title-placeholder {
  margin-bottom: 16px;
  width: 700px;
  font-family: 'MStiffHei PRC';
  font-size: 32px;
  color: #fff;
  line-height: 40px;
  text-align: center;
}

.location-icon {
  width: 20px;
  height: 20px;
  background: url('@/assets/header/location-icon.png') no-repeat center;
  background-size: cover;
}

.menus-icon {
  width: 32px;
  height: 32px;
  background: url('@/assets/header/menus-icon.png') no-repeat center;
  background-size: cover;
  transition: transform 0.3s ease;
}

.menus-icon:hover {
  transform: scale(1.1);
  cursor: pointer;
}
.settings-icon {
  width: 32px;
  height: 32px;
  background: url('@/assets/header/settings.svg') no-repeat center;
  background-size: cover;
}

.info-text {
  font-family: Source Han Sans;
  font-size: 18px;
  font-weight: bold;
  line-height: 26px;
  letter-spacing: normal;
  background:
    linear-gradient(180deg, rgba(119, 168, 217, 0) 0%, rgba(119, 168, 217, 0) 20%, rgba(119, 168, 217, 0.6) 82%),
    #ffffff;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* text-fill-color: transparent; */
}
</style>
