<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <IndustryOverview />
      <SupervisionInspection />
      <HouseholdInspection />
    </div>

    <!-- 中央地图区域 -->
    <div class="center-panel">
      <!-- 地图控制组件 -->
      <LayersTool v-model="layersData" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <RiskHazardMonitoring />
      <OldPipeNetworkRenovation />
      <CylinderFillingMonitoring />
    </div>

    <!-- 弹窗 -->
    <IndustryInfoDialog :open="industryInfoOpen" @close="industryInfoOpen = false" />
    <StationDialog :open="stationInfoOpen" @close="stationInfoOpen = false" />
    <PipelineDialog :open="pipelineInfoOpen" @close="pipelineInfoOpen = false" />
    <AreaDialog :open="areaInfoOpen" @close="areaInfoOpen = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import IndustryOverview from './components/IndustryOverview.vue'
import HouseholdInspection from './components/HouseholdInspection.vue'
import SupervisionInspection from './components/SupervisionInspection.vue'
import RiskHazardMonitoring from './components/RiskHazardMonitoring.vue'
import OldPipeNetworkRenovation from './components/OldPipeNetworkRenovation.vue'
import CylinderFillingMonitoring from './components/CylinderFillingMonitoring.vue'
import LayersTool from '@/components/LayersTool.vue'
import IndustryInfoDialog from '@/components/ue-dialog/IndustryInfoDialog.vue'
import StationDialog from '@/components/ue-dialog/StationDialog.vue'
import PipelineDialog from '@/components/ue-dialog/PipelineDialog.vue'
import AreaDialog from '@/components/ue-dialog/AreaDialog.vue'
import { layerData } from './layerData'

// 弹窗状态
// import type { Ref } from 'vue'
// import type { IndustryInfo, StationInfo, PipelineInfo, AreaInfo } from '@/types/layerDialog'

const industryInfoOpen = ref(false)
// const industryInfoData: Ref<IndustryInfo | null> = ref(null)
const stationInfoOpen = ref(false)
// const stationInfoData: Ref<StationInfo | null> = ref(null)
const pipelineInfoOpen = ref(false)
// const pipelineInfoData: Ref<PipelineInfo | null> = ref(null)
const areaInfoOpen = ref(false)
// const areaInfoData: Ref<AreaInfo | null> = ref(null)

// 图层数据
const layersData = ref(layerData)

// 模拟API调用
// const fetchIndustryInfo = async (id: string) => {
//   console.log('Fetching industry info for id:', id)
//   // 模拟网络请求
//   await new Promise(resolve => setTimeout(resolve, 200))
//   // 模拟返回的数据
//   return {
//     id,
//     area: [
//       [115.91, 39.05],
//       [115.92, 39.06],
//       [115.93, 39.05],
//       [115.91, 39.05],
//     ],
//   }
// }

// const fetchStationInfo = async (id: string) => {
//   console.log('Fetching station info for id:', id)
//   await new Promise(resolve => setTimeout(resolve, 200))
//   return {
//     id,
//     name: `场站 ${id}`,
//     personInCharge: '李**',
//     address: '模拟地址',
//     property: '调压站',
//     phone: '13800138000',
//   }
// }

// const fetchPipelineInfo = async (id: string) => {
//   console.log('Fetching pipeline info for id:', id)
//   await new Promise(resolve => setTimeout(resolve, 200))
//   return {
//     id,
//     name: `管线 ${id}`,
//     pipeType: '中压',
//     industry: '模拟燃气公司',
//     material: 'PE',
//     burialDepth: '1.5m',
//     commissioningTime: '2022-01-01',
//     node: 'NodeA-NodeB',
//     personInCharge: '王**',
//     phone: '13900139000',
//   }
// }

// const fetchAreaInfo = async (id: string) => {
//   console.log('Fetching area info for id:', id)
//   await new Promise(resolve => setTimeout(resolve, 200))
//   return {
//     id,
//     title: `风险区域 ${id}`,
//     address: '模拟风险地址',
//     personInCharge: '赵**',
//     riskLevel: '二级风险',
//     phone: '13700137000',
//     videos: [{ url: 'https://example.com/video.mp4' }],
//   }
// }

// 交互处理
// const handleClickPoint = async (data: { id: string }) => {
//   const { id } = data
//   const idNumber = parseInt(id.replace(/\D/g, ''), 10)

//   if (idNumber >= 12210 && idNumber <= 12220) {
//     industryInfoData.value = await fetchIndustryInfo(id)
//     industryInfoOpen.value = true
//   } else if (idNumber >= 12271 && idNumber <= 12284) {
//     stationInfoData.value = await fetchStationInfo(id)
//     stationInfoOpen.value = true
//   }
// }

// const handleClickLine = async (data: { id: string }) => {
//   pipelineInfoData.value = await fetchPipelineInfo(data.id)
//   pipelineInfoOpen.value = true
// }

// const handleClickPlant = async (data: { id: string }) => {
//   areaInfoData.value = await fetchAreaInfo(data.id)
//   areaInfoOpen.value = true
// }

onMounted(() => {
  // todo
})

onUnmounted(() => {
  // 清除所有图层
})
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 96px;
  left: 24px;
  right: 24px;
  height: calc(100% - 96px);
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 744px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.left-panel {
  left: 0;
}

.right-panel {
  right: 0;
}

.center-panel {
  position: absolute;
  top: 0;
  left: 768px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
