<template>
  <div class="panel-container">
    <div class="panel-header">投诉渠道分析</div>
    <div ref="chartRef" class="panel-content p-2">
      <div class="w-full h-full" ref="container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, onBeforeUnmount, ref } from 'vue'

const container = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null

const init = () => {
  if (!container.value) return
  chart = echarts.init(container.value)
  const data = [
    { name: '网络投诉', value: 335 },
    { name: '电话投诉', value: 310 },
    { name: '社会舆情', value: 234 },
    { name: '其他', value: 135 },
    { name: '上级转办', value: 154 },
  ]

  chart.setOption({
    grid: { left: 0, right: 0, top: 0, bottom: 0 },
    tooltip: { trigger: 'item' },
    legend: {
      orient: 'vertical',
      right: 20,
      top: 'center',
      textStyle: { color: '#66FFFF' },
    },
    series: [
      {
        name: '渠道',
        type: 'pie',
        radius: ['38%', '65%'],
        center: ['38%', '50%'],
        label: { color: '#AEE', formatter: '{b}\n{d}%' },
        data,
      },
    ],
  })
}

onMounted(() => init())
onBeforeUnmount(() => chart?.dispose())
</script>

<style scoped>
@import '@/styles/index.css';
.panel-content > div {
  width: 100%;
  height: 100%;
}
</style>
