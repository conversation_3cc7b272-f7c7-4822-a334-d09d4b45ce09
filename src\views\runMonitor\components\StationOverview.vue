<template>
  <div class="panel-container">
    <div class="panel-header">站点概况</div>
    <div class="p-4 panel-content">
      <div class="flex flex-wrap justify-around status-indicators gap-y-1">
        <div v-for="(i, index) in statusData" :key="index" class="status-item animate-pulse" :class="i.icon">
          <div class="status-value">{{ i.value }}</div>
          <div class="status-label">{{ i.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const statusData = ref([
  { label: '场站总数', value: 41, icon: 'blue' },
  { label: '门站数量', value: 9, icon: 'green' },
  { label: '调压站数量', value: 16, icon: 'yellow' },
  { label: '充装站', value: 2, icon: 'blue' },
  { label: '储备站', value: 10, icon: 'green' },
  { label: 'CNG加气站', value: 4, icon: 'yellow' },
])
</script>

<style scoped>
@import '@/styles/index.css';

.status-indicators {
  width: 100%;
  height: 216px;
}

.status-item {
  display: flex;
  width: 227px;
  height: 104px;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(64, 159, 255, 0) 0%, #409fff 49%, rgba(64, 159, 255, 0) 100%) 1;
}
.status-item.blue {
  background: url('@/assets/run-monitor/station-overview-blue.png') no-repeat center;
  background-size: 90px 40px;
}
.status-item.yellow {
  background: url('@/assets/run-monitor/station-overview-green.png') no-repeat center;
  background-size: 90px 40px;
}
.status-item.green {
  background: url('@/assets/run-monitor/station-overview-yellow.png') no-repeat center;
  background-size: 90px 40px;
}

.status-value {
  font-family: NotoSansSC;
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  color: #fff;
  white-space: nowrap;
}

.status-label {
  font-family: NotoSansSC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  padding-bottom: 4px;
  text-align: center;
  letter-spacing: normal;
  color: #fff;
}
</style>
