<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  // SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { MapPin, Loader } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import * as echarts from 'echarts'
import Map from '@/components/Map.vue'

const props = defineProps<{
  open: boolean
}>()
const emit = defineEmits<{
  (e: 'close'): void
}>()

const isLoading = ref<boolean>(false)
const selectedTime = ref<string>('10')
const selectedArea = ref<string>('a1')
const leakPosition = ref<{ lng: number; lat: number } | null>(null)
const mapRef = ref<InstanceType<typeof Map> | null>(null)

const analysisData = ref({
  source: [
    { label: '所属燃气公司', value: '__' },
    { label: '企业法人电话', value: '__' },
    { label: '泄漏点隐患管线', value: '__' },
    { label: '泄漏点隐患管段', value: '__' },
  ],
  diffusion: [
    { label: '扩散面积', value: '__' },
    { label: '易爆燃波及半径', value: '__' },
    { label: '易爆燃区域浓度', value: '__' },
    { label: '波及管段与节点', value: '__' },
  ],
  emergency: [
    { label: '最近消防设施', value: '__' },
    { label: '最近应急队伍', value: '__' },
    { label: '应急队伍负责人', value: '__' },
    { label: '负责人联系电话', value: '__' },
    { label: '抢修车辆', value: '__' },
    { label: '车辆负责人', value: '__' },
    { label: '负责人电话', value: '__' },
    { label: '医院信息', value: '__' },
  ],
  explosion: [
    { label: '估算波及半径', value: '__' },
    { label: '爆炸中心压力', value: '__' },
    { label: '压力上升速度', value: '__' },
    { label: '估算波及人口', value: '__' },
  ],
})

const chartData = ref([
  { name: '152', value: 0.4 },
  { name: '152', value: 0.7 },
  { name: '152', value: 0.5 },
  { name: '152', value: 0.8 },
  { name: '152', value: 0.6 },
  { name: '152', value: 0.4 },
])

const chartContainer = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

watch(
  () => props.open,
  newVal => {
    if (!newVal) {
      // 弹窗关闭时，销毁图表实例
      chart?.dispose()
      chart = null
    }
  },
)

// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  // 当接收到 open 为 false 的更新时，触发 close 事件
  if (!open) {
    // 重置数据
    leakPosition.value = null
    analysisData.value = {
      source: [
        { label: '所属燃气公司', value: '__' },
        { label: '企业法人电话', value: '__' },
        { label: '泄漏点隐患管线', value: '__' },
        { label: '泄漏点隐患管段', value: '__' },
      ],
      diffusion: [
        { label: '扩散面积', value: '__' },
        { label: '易爆燃波及半径', value: '__' },
        { label: '易爆燃区域浓度', value: '__' },
        { label: '波及管段与节点', value: '__' },
      ],
      emergency: [
        { label: '最近消防设施', value: '__' },
        { label: '最近应急队伍', value: '__' },
        { label: '应急队伍负责人', value: '__' },
        { label: '负责人联系电话', value: '__' },
        { label: '抢修车辆', value: '__' },
        { label: '车辆负责人', value: '__' },
        { label: '负责人电话', value: '__' },
        { label: '医院信息', value: '__' },
      ],
      explosion: [
        { label: '估算波及半径', value: '__' },
        { label: '爆炸中心压力', value: '__' },
        { label: '压力上升速度', value: '__' },
        { label: '估算波及人口', value: '__' },
      ],
    }
    // 重置图表数据
    chartData.value = [
      { name: '152', value: 0.4 },
      { name: '152', value: 0.7 },
      { name: '152', value: 0.5 },
      { name: '152', value: 0.8 },
      { name: '152', value: 0.6 },
      { name: '152', value: 0.4 },
    ]
    // 清理地图
    mapRef.value?.destroyHeatMap()
    mapRef.value?.destroyMarker()
    emit('close')
  }
}

const initChart = () => {
  if (chartContainer.value) {
    chart = echarts.init(chartContainer.value)
    setChartOptions()
  }
}

const setChartOptions = () => {
  if (chart) {
    const option = {
      grid: {
        top: '15%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.value.map(item => item.name),
        axisLabel: {
          color: '#99D5FF',
        },
      },
      yAxis: {
        type: 'value',
        name: '单位: MPa',
        nameTextStyle: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10,
        },
        axisLabel: {
          color: '#99D5FF',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(153, 213, 255, 0.2)',
          },
        },
      },
      series: [
        {
          data: chartData.value.map(item => item.value),
          type: 'line',
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(140, 255, 255, 0.45)',
              },
              {
                offset: 1,
                color: 'rgba(140, 255, 255, 0)',
              },
            ]),
          },
          lineStyle: {
            color: '#8CFFFF',
          },
          itemStyle: {
            color: '#8CFFFF',
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
      },
    }
    chart.setOption(option)
  }
}

// 处理地图点击事件
const handleMapClick = (position: { lng: number; lat: number }) => {
  leakPosition.value = position
}

// 开始计算
const handleStartCalculation = () => {
  if (!leakPosition.value) {
    toast.error('请先在地图上选择泄漏点！', {
      // description: '请先在地图上选择泄漏点！',
      position: 'top-center',
      duration: 2000,
      style: {
        background: 'rgb(248,113,113,0.6)',
        backdropFilter: 'blur(8px)',
        color: 'white',
        borderColor: 'rgb(248,113,113)',
      },
    })
    return
  }
  isLoading.value = true

  // 1. 生成以标记点为中心的热力图数据
  const heatData: { lnglat: [number, number]; num: number }[] = []
  const center = leakPosition.value
  // 中心点权重最高
  heatData.push({ lnglat: [center.lng, center.lat], num: 100 })

  // 生成同心圆扩散点
  const radiuses = [6] // 扩散半径
  const weights = [80, 60, 40, 20] // 对应权重
  const pointsPerCircle = 16 // 每圈的点数

  radiuses.forEach((radius, i) => {
    for (let j = 0; j < pointsPerCircle; j++) {
      const angle = (j / pointsPerCircle) * 2 * Math.PI
      const lng = center.lng + radius * Math.cos(angle)
      const lat = center.lat + radius * Math.sin(angle)
      heatData.push({
        lnglat: [lng, lat] as [number, number],
        num: weights[i] + (Math.random() - 0.5) * 10, // 加入少量随机性
      })
    }
  })
  // 传递数据到 map 组件
  mapRef.value?.showHeatMap(heatData)

  // 2. 模拟填充数据
  analysisData.value = {
    source: [
      { label: '所属燃气公司', value: '华润燃气有限公司' },
      { label: '企业法人电话', value: '19900000000' },
      { label: '泄漏点隐患管线', value: 'pipeline-001' },
      { label: '泄漏点隐患管段', value: '265' },
    ],
    diffusion: [
      { label: '扩散面积', value: '56.29m²' },
      { label: '易爆燃波及半径', value: '100m' },
      { label: '易爆燃区域浓度', value: '500ppm' },
      { label: '波及管段与节点', value: 'pipeline-001, node-001' },
    ],
    emergency: [
      { label: '最近消防设施', value: '002' },
      { label: '最近应急队伍', value: '应急队伍-001' },
      { label: '应急队伍负责人', value: '张三' },
      { label: '负责人联系电话', value: '19900000001' },
      { label: '抢修车辆', value: '冀A19283' },
      { label: '车辆负责人', value: '李四' },
      { label: '负责人电话', value: '19900000002' },
      { label: '医院信息', value: '医院-001' },
    ],
    explosion: [
      { label: '估算波及半径', value: '150m' },
      { label: '爆炸中心压力', value: '0.844MPa' },
      { label: '压力上升速度', value: '9.2MPa/s' },
      { label: '估算波及人口', value: '50人' },
    ],
  }

  // 3. 模拟更新图表
  chartData.value = Array.from({ length: 6 }, () => ({
    name: Math.floor(Math.random() * 100 + 100).toString(),
    value: parseFloat(Math.random().toFixed(1)),
  }))
  // 确保图表已初始化后再更新
  if (chart) {
    setChartOptions()
  } else {
    initChart()
  }
  isLoading.value = false
}

onUnmounted(() => {
  // 确保组件销毁时也清理实例
  chart?.dispose()
})
</script>
<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[1200px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">测算预演</DialogTitle>
        <DialogDescription as="div" class="p-6 text-sm text-white">
          <!-- Top Section -->
          <div class="flex gap-8">
            <!-- Left Inputs -->
            <div class="flex flex-col gap-4 w-[320px]">
              <div class="flex items-center justify-between">
                <span class="text-[#99D5FF]">泄漏点位：</span>
                <span v-if="!leakPosition" class="flex items-center gap-2">
                  地图点选
                  <MapPin class="w-4 h-4" />
                </span>
                <span v-else class="text-xs">{{ leakPosition.lng.toFixed(6) }}, {{ leakPosition.lat.toFixed(6) }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-[#99D5FF]">泄漏时长：</span>
                <!-- <span class="flex items-center gap-2">请选择 <Calendar class="w-4 h-4" /></span> -->
                <Select v-bind:model-value="selectedTime">
                  <SelectTrigger class="w-40 h-10 text-sm dropdown-btn">
                    <SelectValue placeholder="请选择" />
                  </SelectTrigger>
                  <SelectContent class="text-[#99D5FF]">
                    <SelectGroup>
                      <!-- <SelectLabel>Fruits</SelectLabel> -->
                      <SelectItem value="10">10分钟</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-[#99D5FF]">泄漏区域：</span>
                <!-- <span class="flex items-center gap-2">请选择 <SquarePen class="w-4 h-4" /></span> -->
                <Select v-bind:model-value="selectedArea">
                  <SelectTrigger class="w-40 h-10 text-sm dropdown-btn">
                    <SelectValue placeholder="请选择" />
                  </SelectTrigger>
                  <SelectContent class="text-[#99D5FF]">
                    <SelectGroup>
                      <!-- <SelectLabel>Fruits</SelectLabel> -->
                      <SelectItem value="a1">区域1</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <Button
                class="mt-4 w-full bg-[#77A8D9] rounded-sm hover:bg-[#77A8D9]/80 text-white outline-none"
                @click="handleStartCalculation"
              >
                <Loader v-show="isLoading" class="w-4 h-4 mr-2 animate-spin" />
                开始计算
              </Button>
            </div>
            <!-- Right Map Placeholder -->
            <div class="flex-1 bg-black/20 rounded h-[306px]">
              <Map ref="mapRef" :zoom="18" :pitch="20" @map-click="handleMapClick" :clickable="true" />
            </div>
          </div>

          <!-- Bottom Section -->
          <div class="grid grid-cols-4 gap-6 mt-8">
            <!-- Source Analysis -->
            <div class="flex flex-col gap-3">
              <h3 class="text-lg font-bold text-white">溯源分析</h3>
              <div v-for="item in analysisData.source" :key="item.label" class="flex text-sm">
                <p class="w-[8em] text-right text-[#99D5FF]">{{ item.label }}：</p>
                <p>{{ item.value }}</p>
              </div>
            </div>
            <!-- Diffusion Analysis -->
            <div class="flex flex-col gap-3">
              <h3 class="text-lg font-bold text-white">扩散分析</h3>
              <div v-for="item in analysisData.diffusion" :key="item.label" class="flex text-sm">
                <p class="w-[8em] text-right text-[#99D5FF]">{{ item.label }}：</p>
                <p>{{ item.value }}</p>
              </div>
            </div>
            <!-- Emergency Analysis -->
            <div class="flex flex-col gap-3">
              <h3 class="text-lg font-bold text-white">应急分析</h3>
              <div v-for="item in analysisData.emergency" :key="item.label" class="flex text-sm">
                <p class="w-[8em] text-right text-[#99D5FF]">{{ item.label }}：</p>
                <p>{{ item.value }}</p>
              </div>
            </div>
            <!-- Explosion Test -->
            <div class="flex flex-col gap-3">
              <h3 class="text-lg font-bold text-white">爆燃测试</h3>
              <div v-for="item in analysisData.explosion" :key="item.label" class="flex text-sm">
                <p class="w-[8em] text-right text-[#99D5FF]">{{ item.label }}：</p>
                <p>{{ item.value }}</p>
              </div>
              <div class="h-[188px] mt-4">
                <div ref="chartContainer" class="w-full h-full"></div>
              </div>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>
<style scoped>
.title-bg {
  background: url('@/assets/dialog/title-bg-1200.png') no-repeat 0 0;
  background-size: cover;
  font-family: MStiffHei PRC;
}
.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  outline: none;
}
</style>
