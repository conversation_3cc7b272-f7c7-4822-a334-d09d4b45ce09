<template>
  <div class="panel-container">
    <div class="panel-header">
      <div class="header-title">巡检概况</div>
    </div>
    <div class="panel-content">
      <div class="user-overview-container">
        <div v-for="(item, index) in userStats" :key="index" class="user-card">
          <div class="card-content">
            <div class="card-icon">
              <img :src="item.icon" alt="" />
            </div>
            <div class="card-info-row">
              <div class="card-title">{{ item.title }}</div>
              <div class="card-value-container">
                <span class="card-value" :style="{ color: item.color }">{{ item.value }}</span>
                <span class="card-unit">{{ item.unit }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 图标数据配置 - 使用图片路径
const iconData = {
  person: '/src/assets/patrol-inspection/person.svg',
  region: '/src/assets/patrol-inspection/region.svg',
  danger: '/src/assets/patrol-inspection/danger.svg',
  done: '/src/assets/patrol-inspection/done.svg',
  iconDown: '/src/assets/patrol-inspection/icon-down.svg',
}

// 用户概况数据
const userStats = ref([
  {
    type: 'total',
    title: '巡查人员',
    value: '86',
    icon: iconData.person,
    color: '#3AC0FF',
    unit: '人',
  },
  {
    type: 'pipeline',
    title: '覆盖区域',
    value: '86',
    icon: iconData.region,
    color: '#FFD700',
    unit: '个',
  },
  {
    type: 'bottle',
    title: '发现隐患',
    value: '86',
    icon: iconData.danger,
    color: '#FF5B5B',
    unit: '处',
  },
  {
    type: 'business',
    title: '整改完成',
    value: '86',
    icon: iconData.done,
    color: '#3AC07B',
    unit: '处',
  },
])
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.panel-content {
  padding: 16px;
}

.user-overview-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  width: 100%;
  height: 100%;
}

.user-card {
  position: relative;
  width: 348px;
  height: 104px;
  overflow: hidden;
  background-image: url('@/assets/patrol-inspection/xj-bg.svg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;

  .card-content {
    position: absolute;
    top: 27px;
    left: 32px;
    width: 284px;
    height: 50px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;

    .card-icon {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;

      > img {
        width: 48px;
        height: 48px;
        left: 0;
        top: 0;
        position: relative;
      }
    }

    .card-info-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 12px;
      width: 200px;
      justify-content: space-between;

      .card-title {
        color: #ffff;
        font-family: 'Noto Sans SC';
        font-size: 16px;
        line-height: 22px;
        white-space: nowrap;
        font-weight: lighter;
        width: auto;
        margin-right: 8px;
      }
      .card-value-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 2px;
        .card-value {
          color: #ffd700;
          font-family: 'DINPro';
          font-size: 20px;
          font-weight: bold;
          line-height: 28px;
        }
        .card-unit {
          color: #fff;
          font-size: 12px;
          margin-left: 4px;
          margin-top: 2px;
        }
      }
    }
  }

  .card-border-bottom {
    position: absolute;
    left: 30px;
    bottom: 0px;
    width: 288px;
    height: 12px;
  }

  .card-side-lines {
    .side-line {
      position: absolute;
      top: 0px;
      width: 5.5px;
      height: 115px;

      &.left {
        left: 0px;
      }

      &.right {
        right: 0px;
      }
    }
  }
}
</style>
