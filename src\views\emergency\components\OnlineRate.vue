<script setup lang="ts">
import { ref } from 'vue'

const online = ref(75)
const offline = ref(25)
const rate = ref(75)
</script>
<template>
  <div class="panel-container">
    <div class="panel-header">应急监控实时在线率</div>
    <div class="flex items-center justify-around p-4 panel-content">
      <div class="w-[200px] h-[200px] flex items-center justify-center flex-col relative top-[-10px] rate-bg">
        <p>
          <span class="text-[32px] font-bold text-white">{{ rate }}%</span>
        </p>
        <p>
          <span class="text-sm text-white">在线率</span>
        </p>
      </div>
      <div>
        <div class="flex items-center justify-between pr-4 pl-19 ind-online">
          <span class="text-base text-white">监控在线数(个)</span>
          <p>
            <span class="text-xl font-bold text-[#47EBEB]">{{ online }}</span>
          </p>
        </div>
        <div class="flex items-center justify-between pr-4 pl-19 ind-online">
          <span class="text-sm text-white">监控离线数(%)</span>
          <span class="text-xl font-bold text-[#47EBEB]">{{ offline }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
@import '@/styles/index.css';

.rate-bg {
  background: url('@/assets/emergency/online-rate-ring.png') no-repeat center;
  background-size: cover;

  &::after {
    content: '';
    display: block;
    width: 200px;
    height: 40px;
    position: absolute;
    bottom: -20px;
    left: 0;
    /* transform: translate(-50%, -50%); */
    pointer-events: none;
    z-index: 1;
    background: url('@/assets/emergency/online-rate-bottom.png') no-repeat center;
    background-size: cover;
  }
}
.ind-online {
  width: 374px;
  height: 80px;
  background: url('@/assets/emergency/online-bg.png') no-repeat center;
  background-size: cover;
}
</style>
