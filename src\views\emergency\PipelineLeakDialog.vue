<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Loader } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import * as echarts from 'echarts'
import Map from '@/components/Map.vue'

const props = defineProps<{
  open: boolean
  data?: any
}>()
const emit = defineEmits<{
  (e: 'close'): void
}>()

const isLoading = ref<boolean>(false)
const mapRef = ref<InstanceType<typeof Map> | null>(null)
const leakData = ref({
  id: 'INV001',
  time: '2024-08-06 10:30:00',
  duration: '30分钟',
  position: { lng: 115.915, lat: 39.055 },
  device: '管网泄漏探测器-01',
})
const leakPosition = ref<{ lng: number; lat: number } | null>(null)

const analysisData = ref({
  source: [
    { label: '所属燃气公司', value: '__' },
    { label: '企业法人电话', value: '__' },
    { label: '泄漏点隐患管线', value: '__' },
    { label: '泄漏点隐患管段', value: '__' },
  ],
  diffusion: [
    { label: '扩散面积', value: '__' },
    { label: '易爆燃波及半径', value: '__' },
    { label: '易爆燃区域浓度', value: '__' },
    { label: '波及管段与节点', value: '__' },
  ],
  emergency: [
    { label: '最近消防设施', value: '__' },
    { label: '最近应急队伍', value: '__' },
    { label: '应急队伍负责人', value: '__' },
    { label: '负责人联系电话', value: '__' },
    { label: '抢修车辆', value: '__' },
    { label: '车辆负责人', value: '__' },
    { label: '负责人电话', value: '__' },
    { label: '医院信息', value: '__' },
  ],
  explosion: [
    { label: '估算波及半径', value: '__' },
    { label: '爆炸中心压力', value: '__' },
    { label: '压力上升速度', value: '__' },
    { label: '估算波及人口', value: '__' },
  ],
})

const chartData = ref([
  { name: '152', value: 0.4 },
  { name: '152', value: 0.7 },
  { name: '152', value: 0.5 },
  { name: '152', value: 0.8 },
  { name: '152', value: 0.6 },
  { name: '152', value: 0.4 },
])

const alarmTracebacks = ref([
  { type: '浓度超标', time: '2025-07-30 16:42:30' },
  { type: '浓度超标', time: '2025-07-30 16:42:30' },
  { type: '浓度超标', time: '2025-07-30 16:42:30' },
  { type: '浓度超标', time: '2025-07-30 16:42:30' },
  { type: '浓度超标', time: '2025-07-30 16:42:30' },
  { type: '浓度超标', time: '2025-07-30 16:42:30' },
])

const chartContainer = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

watch(
  () => props.open,
  newVal => {
    if (newVal) {
      leakData.value = props.data || {
        id: 'INV001',
        time: '2024-08-06 10:30:00',
        duration: '30分钟',
        position: { lng: 115.915, lat: 39.055 },
        device: '管网泄漏探测器-01',
      }
      leakPosition.value = props.data?.position
    } else {
      // 弹窗关闭时，销毁图表实例
      chart?.dispose()
      chart = null
    }
  },
)

const onMapReady = () => {
  if (mapRef.value && props.data?.position) {
    mapRef.value.addMarker(props.data.position)
  }
}

// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  // 当接收到 open 为 false 的更新时，触发 close 事件
  if (!open) {
    // 重置数据
    analysisData.value = {
      source: [
        { label: '所属燃气公司', value: '__' },
        { label: '企业法人电话', value: '__' },
        { label: '泄漏点隐患管线', value: '__' },
        { label: '泄漏点隐患管段', value: '__' },
      ],
      diffusion: [
        { label: '扩散面积', value: '__' },
        { label: '易爆燃波及半径', value: '__' },
        { label: '易爆燃区域浓度', value: '__' },
        { label: '波及管段与节点', value: '__' },
      ],
      emergency: [
        { label: '最近消防设施', value: '__' },
        { label: '最近应急队伍', value: '__' },
        { label: '应急队伍负责人', value: '__' },
        { label: '负责人联系电话', value: '__' },
        { label: '抢修车辆', value: '__' },
        { label: '车辆负责人', value: '__' },
        { label: '负责人电话', value: '__' },
        { label: '医院信息', value: '__' },
      ],
      explosion: [
        { label: '估算波及半径', value: '__' },
        { label: '爆炸中心压力', value: '__' },
        { label: '压力上升速度', value: '__' },
        { label: '估算波及人口', value: '__' },
      ],
    }
    chartData.value = [
      { name: '152', value: 0.4 },
      { name: '152', value: 0.7 },
      { name: '152', value: 0.5 },
      { name: '152', value: 0.8 },
      { name: '152', value: 0.6 },
      { name: '152', value: 0.4 },
    ]
    initChart()
    mapRef.value?.destroyHeatMap()
    mapRef.value?.destroyMarker()
    emit('close')
  }
}

const setChartOptions = () => {
  if (chart) {
    chart.setOption({
      grid: {
        top: '15%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.value.map((item: any) => item.name),
        axisLabel: {
          color: '#99D5FF',
        },
      },
      yAxis: {
        type: 'value',
        name: '单位: MPa',
        nameTextStyle: {
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: 10,
        },
        axisLabel: {
          color: '#99D5FF',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(153, 213, 255, 0.2)',
          },
        },
      },
      series: [
        {
          data: chartData.value.map(item => item.value),
          type: 'line',
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(140, 255, 255, 0.45)',
              },
              {
                offset: 1,
                color: 'rgba(140, 255, 255, 0)',
              },
            ]),
          },
          lineStyle: {
            color: '#8CFFFF',
          },
          itemStyle: {
            color: '#8CFFFF',
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
      },
    })
  }
}

const initChart = () => {
  if (chartContainer.value) {
    chart = echarts.init(chartContainer.value)
    setChartOptions()
  }
}

// 开始计算
const handleStartCalculation = () => {
  if (!leakPosition.value) {
    toast.error('缺少泄漏点！', {
      // description: '缺少泄漏点！',
      position: 'top-center',
      duration: 2000,
      style: {
        background: 'rgb(248,113,113,0.6)',
        backdropFilter: 'blur(8px)',
        color: 'white',
        borderColor: 'rgb(248,113,113)',
      },
    })
    return
  }

  isLoading.value = true

  // 1. 生成以标记点为中心的热力图数据
  const heatData: { lnglat: [number, number]; num: number }[] = []
  const center = leakPosition.value
  // 中心点权重最高
  heatData.push({ lnglat: [center.lng, center.lat] as [number, number], num: 100 })

  // 生成同心圆扩散点
  const radiuses = [0.2] // 扩散半径
  const weights = [80, 60, 40, 20] // 对应权重
  const pointsPerCircle = 16 // 每圈的点数

  radiuses.forEach((radius, i) => {
    for (let j = 0; j < pointsPerCircle; j++) {
      const angle = (j / pointsPerCircle) * 2 * Math.PI
      const lng = center.lng + radius * Math.cos(angle)
      const lat = center.lat + radius * Math.sin(angle)
      heatData.push({
        lnglat: [lng, lat] as [number, number],
        num: weights[i] + (Math.random() - 0.5) * 1, // 加入少量随机性
      })
    }
  })
  // 传递数据到 map 组件
  mapRef.value?.showHeatMap(heatData)

  // 2. 模拟填充数据
  analysisData.value = {
    source: [
      { label: '所属燃气公司', value: '华润燃气有限公司' },
      { label: '企业法人电话', value: '19900000000' },
      { label: '泄漏点隐患管线', value: 'pipeline-001' },
      { label: '泄漏点隐患管段', value: '265' },
    ],
    diffusion: [
      { label: '扩散面积', value: '56.29m²' },
      { label: '易爆燃波及半径', value: '100m' },
      { label: '易爆燃区域浓度', value: '500ppm' },
      { label: '波及管段与节点', value: 'pipeline-001, node-001' },
    ],
    emergency: [
      { label: '最近消防设施', value: '002' },
      { label: '最近应急队伍', value: '应急队伍-001' },
      { label: '应急队伍负责人', value: '张三' },
      { label: '负责人联系电话', value: '19900000001' },
      { label: '抢修车辆', value: '冀A19283' },
      { label: '车辆负责人', value: '李四' },
      { label: '负责人电话', value: '19900000002' },
      { label: '医院信息', value: '医院-001' },
    ],
    explosion: [
      { label: '估算波及半径', value: '150m' },
      { label: '爆炸中心压力', value: '0.844MPa' },
      { label: '压力上升速度', value: '9.2MPa/s' },
      { label: '估算波及人口', value: '50人' },
    ],
  }

  // 告警回溯数据
  alarmTracebacks.value = [
    { type: '浓度超标', time: '2025-07-30 16:42:30' },
    { type: '浓度超标', time: '2025-07-30 16:42:30' },
    { type: '浓度超标', time: '2025-07-30 16:42:30' },
    { type: '浓度超标', time: '2025-07-30 16:42:30' },
    { type: '浓度超标', time: '2025-07-30 16:42:30' },
    { type: '浓度超标', time: '2025-07-30 16:42:30' },
  ]

  // 3. 模拟更新图表
  chartData.value = Array.from({ length: 6 }, () => ({
    name: Math.floor(Math.random() * 100 + 100).toString(),
    value: parseFloat(Math.random().toFixed(1)),
  }))
  if (chart) {
    setChartOptions()
  } else {
    initChart() // 重新渲染图表
  }
  isLoading.value = false
}

// onMounted(() => {
//   initChart()
// })
onUnmounted(() => {
  chart?.dispose()
})
</script>
<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-[#2A384D]/20 backdrop-blur-sm" />
    <DialogContent
      class="sm:max-w-[1200px] p-0 bg-[#2A384D]/60 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">泄漏测算</DialogTitle>
        <DialogDescription as="div" class="p-6 max-h-[820px] overflow-auto custom-scrollbar text-sm text-white">
          <!-- Top Section -->
          <div class="flex gap-8">
            <!-- Left Inputs -->
            <div class="flex flex-col gap-4 w-[320px]">
              <div class="flex items-center justify-between">
                <span class="text-[#99D5FF]">泄漏点位：</span>
                <span class="flex items-center gap-2">{{ leakData.position?.lng }}, {{ leakData.position?.lat }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-[#99D5FF]">泄漏时长：</span>
                <span class="flex items-center gap-2">{{ leakData.duration }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-[#99D5FF]">报警设备：</span>
                <span class="flex items-center gap-2">{{ leakData.device }}</span>
              </div>
              <Button
                class="mt-4 w-full bg-[#77A8D9] rounded-sm hover:bg-[#77A8D9]/80 text-white outline-none"
                @click="handleStartCalculation"
              >
                <Loader v-show="isLoading" class="w-4 h-4 animate-spin" />
                开始计算
              </Button>
            </div>
            <!-- Right Map Placeholder -->
            <div class="flex-1 bg-black/20 rounded h-[306px]">
              <Map
                ref="mapRef"
                :center="[leakData.position?.lng, leakData.position?.lat]"
                :zoom="18"
                :pitch="20"
                @map-ready="onMapReady"
              />
            </div>
          </div>

          <!-- Bottom Section -->
          <div class="grid grid-cols-4 gap-6 mt-8">
            <!-- Source Analysis -->
            <div class="flex flex-col gap-3">
              <h3 class="text-lg font-bold text-white">溯源分析</h3>
              <div v-for="item in analysisData.source" :key="item.label" class="flex text-sm">
                <p class="w-[8em] text-right text-[#99D5FF]">{{ item.label }}：</p>
                <p>{{ item.value }}</p>
              </div>
            </div>
            <!-- Diffusion Analysis -->
            <div class="flex flex-col gap-3">
              <h3 class="text-lg font-bold text-white">扩散分析</h3>
              <div v-for="item in analysisData.diffusion" :key="item.label" class="flex text-sm">
                <p class="w-[8em] text-right text-[#99D5FF]">{{ item.label }}：</p>
                <p>{{ item.value }}</p>
              </div>
            </div>
            <!-- Emergency Analysis -->
            <div class="flex flex-col gap-3">
              <h3 class="text-lg font-bold text-white">应急分析</h3>
              <div v-for="item in analysisData.emergency" :key="item.label" class="flex text-sm">
                <p class="w-[8em] text-right text-[#99D5FF]">{{ item.label }}：</p>
                <p>{{ item.value }}</p>
              </div>
            </div>
            <!-- Explosion Test -->
            <div class="flex flex-col gap-3">
              <h3 class="text-lg font-bold text-white">爆燃测试</h3>
              <div v-for="item in analysisData.explosion" :key="item.label" class="flex text-sm">
                <p class="w-[8em] text-right text-[#99D5FF]">{{ item.label }}：</p>
                <p>{{ item.value }}</p>
              </div>
              <div class="h-[188px] mt-4">
                <div ref="chartContainer" class="w-full h-full"></div>
              </div>
            </div>
          </div>
          <!-- 告警回溯 -->
          <div>
            <div class="mb-4 text-lg text-[#99D5FF] font-bold">告警回溯</div>
            <div class="relative mb-6">
              <!-- 时间轴线 -->
              <div class="absolute top-11 left-0 right-0 h-[4px] bg-[#99D5FF]/50"></div>
              <!-- 时间轴节点 -->
              <div class="relative flex justify-between">
                <div v-for="(item, index) in alarmTracebacks" :key="index" class="flex flex-col items-center">
                  <div class="text-[#99D5FF] mb-2">{{ item.type }}</div>
                  <div class="w-10 h-10 rounded-full bg-[#99D5FF] z-10 text-center leading-10">报警</div>
                  <div class="mt-2 text-[#99D5FF] text-xs text-center">
                    <div>{{ item.time.split(' ')[0] }}</div>
                    <div>{{ item.time.split(' ')[1] }}</div>
                  </div>
                </div>
              </div>
              <!-- 箭头 -->
            </div>
            <Button
              class="mb-4 w-[280px] mx-auto block bg-[#77A8D9] rounded-sm hover:bg-[#77A8D9]/80 text-white outline-none"
            >
              开始处置
            </Button>
          </div>
          <!-- 处置反馈 -->
          <div>
            <div class="mb-4 text-lg text-[#99D5FF] font-bold">处置反馈</div>
            <div class="grid grid-cols-4 gap-4">
              <div class="flex">
                <div class="text-[#99D5FF]">处置人：</div>
                <div>王文</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">提交时间：</div>
                <div>2025-07-08</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">告警原因：</div>
                <div>软管老化</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">描述：</div>
                <div>无</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">处置方式：</div>
                <div>更换螺纹管</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] whitespace-nowrap">处置取证：</div>
                <div>
                  <div class="flex gap-2">
                    <img
                      src="https://img1.baidu.com/it/u=475744281,3916313639&fm=253&app=138&f=JPEG?w=800&h=1067"
                      alt="处置图片"
                      class="rounded w-30 h-30"
                    />
                    <img
                      src="https://pic.rmb.bdstatic.com/bjh/3f13695da2e/250422/b7919057ce453d11734d024ac413fce7.jpeg"
                      alt="处置图片"
                      class="object-cover rounded w-30 h-30"
                    />
                    <img
                      src="https://img0.baidu.com/it/u=671753217,4078023515&fm=253&app=138&f=JPEG?w=800&h=1062"
                      alt="处置图片"
                      class="object-cover rounded w-30 h-30"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>
<style scoped>
.title-bg {
  background: url('@/assets/dialog/title-bg-1200.png') no-repeat 0 0;
  background-size: cover;
  font-family: MStiffHei PRC;
}
</style>
