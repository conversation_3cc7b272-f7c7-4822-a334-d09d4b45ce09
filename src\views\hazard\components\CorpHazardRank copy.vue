<template>
  <div class="panel-container-col">
    <div class="panel-header">企业重点防控点位排行</div>
    <div class="p-4 panel-content">
      <div ref="chartRef" class="chart-container"></div>
    </div>
    <svg style="visibility: hidden">
      <linearGradient id="textGradient" x1="0" y1="0" x2="0" y2="1">
        <stop offset="0%" stop-color="#4FD1C7"></stop>
        <stop offset="100%" stop-color="#20B2AA"></stop>
      </linearGradient>
      <linearGradient id="barGradient" x1="0" y1="0" x2="1" y2="0">
        <stop offset="0%" stop-color="rgba(79, 209, 199, 0.6)"></stop>
        <stop offset="50%" stop-color="#4FD1C7"></stop>
        <stop offset="100%" stop-color="#20B2AA"></stop>
      </linearGradient>
      <linearGradient id="barBackground" x1="0" y1="0" x2="1" y2="0">
        <stop offset="0%" stop-color="rgba(71, 158, 204, 0.15)"></stop>
        <stop offset="100%" stop-color="rgba(71, 158, 204, 0.25)"></stop>
      </linearGradient>
    </svg>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
// 高亮轮播定时器
let highlightTimer: ReturnType<typeof setInterval> | null = null
// 当前高亮的索引
let currentIndex = -1

const mockData = [
  {
    label: '这是对应内容',
    value: 200,
  },
  {
    label: '这是对应内容',
    value: 150,
  },
  {
    label: '这是对应内容',
    value: 120,
  },
  {
    label: '这是对应内容',
    value: 110,
  },
  {
    label: '这是对应内容',
    value: 80,
  },
]

const option = {
  grid: {
    top: 50,
    left: 40,
    bottom: 0,
    right: 40,
  },
  xAxis: [
    {
      type: 'value',
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
    },
    {
      type: 'value',
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
    },
  ],
  yAxis: [
    {
      type: 'category',
      inverse: true,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 18,
        fontFamily: 'my-num',
        fontWeight: 600,
        color: 'url(#textGradient)',
      },
      data: ['01', '02', '03', '04', '05'],
    },
    {
      inverse: true,
      offset: -30,
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      data: mockData.map(i => ({
        value: 0,
        label: i.label,
      })),
    },
  ],
  series: [
    {
      type: 'bar',
      barWidth: 20,
      label: {
        show: true,
        position: 'top',
        fontSize: 12,
        fontWeight: 'normal',
        formatter: (params: any) => {
          const index = params.dataIndex
          const label = mockData[index].label
          const value = mockData[index].value

          // 计算当前bar的相对宽度（像素估算）
          const maxValue = Math.max(...mockData.map(item => item.value))
          const barWidthRatio = params.value / maxValue
          const estimatedBarWidth = Math.floor(barWidthRatio * 300) // 假设最大bar宽度约300px

          // 根据bar宽度动态计算spacer宽度
          const spacerWidth = Math.max(estimatedBarWidth - 160, 20) // 160是label+value的大概宽度

          return `{label|${label}}{spacer|${''.padEnd(Math.floor(spacerWidth / 8), ' ')}}{value|${value}}`
        },
        rich: {
          label: {
            color: '#fff',
            fontSize: 12,
            fontWeight: 'normal',
          },
          spacer: {
            fontSize: 8,
            color: 'transparent',
          },
          value: {
            color: '#66FFFF',
            fontSize: 12,
            fontWeight: 'bold',
          },
        },
        lineHeight: 16,
      },
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: 'rgba(102, 255, 255, .15)' },
            { offset: 1, color: 'rgba(102, 255, 255, .65)' },
          ],
        },
        borderColor: '#66FFFF',
        borderWidth: 1,
      },
      data: mockData.map(i => i.value),
      z: 5,
    },
    {
      name: 'icon',
      type: 'pictorialBar',
      emphasis: {
        disabled: true,
      },
      symbol: 'triangle',
      symbolRotate: 90,
      symbolPosition: 'end',
      symbolSize: [18, 14],
      symbolOffset: [2, 0],
      z: 10,
      itemStyle: {
        color: '#66FFFF',
      },
      tooltip: { show: false },
      data: mockData.map(i => i.value),
    },
    {
      type: 'pictorialBar',
      symbol: 'rect',
      symbolSize: ['100%', 1],
      symbolPosition: 'center',
      symbolOffset: [0, 0],
      z: 50,
      itemStyle: {
        color: 'transparent',
        borderColor: '#66FFFF',
        borderWidth: 1,
        borderType: 'dashed',
      },
      tooltip: { show: false },
      data: mockData.map(i => i.value),
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)
  chart.setOption(option)
  startHighlightAnimation()
}

// 开始高亮轮播动画
const startHighlightAnimation = () => {
  const dataCount = (option.yAxis[0] as any).data.length
  if (!chart || dataCount === 0) return

  // 清理之前的定时器
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart?.dispatchAction({
      type: 'downplay',
      seriesIndex: 0, // 作用在第一个系列上
      dataIndex: currentIndex,
    })

    // 循环移动到下一个点
    currentIndex = (currentIndex + 1) % dataCount

    // 高亮当前数据项
    chart?.dispatchAction({
      type: 'highlight',
      seriesIndex: 0, // 作用在第一个系列上
      dataIndex: currentIndex,
    })
  }, 2000) // 每隔2秒执行
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>
<style scoped>
@import '@/styles/index.css';
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
