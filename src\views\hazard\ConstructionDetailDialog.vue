<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogOverlay,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
// import { Input } from '@/components/ui/input'
import Map from '@/components/Map.vue'

const props = defineProps<{
  open: boolean
  data?: any
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

// 地图组件的引用
const mapRef = ref<InstanceType<typeof Map> | null>(null)
// 表单数据
const formData = ref({
  processor: '',
  workTime: '',
  reason: '',
  remark: '',
})

// 施工详情数据
const constructionData = ref({
  projectName: '雄安庄村供水管网改造工程',
  contractTime: '2025-05-30',
  completionTime: '年月日',
  constructionUnit: '雄县水务局',
  constructionManager: '李主任',
  unitContact: '19936361523',
  unitManagerContact: '19956891422',
  coordinates: '116.23, 36.56; 119.36, 37.68; 116.23, 37.68; 116.23, 37.68',
  excavationDepth: '2m',
})

// 周边管线数据
const pipelineData = ref([
  {
    name: 'XGA01-ZY-P004',
    location: '中石管网',
    company: '新奥燃气',
    material: '钢铁',
    node: 'P004',
    contact: '19977058561',
  },
  {
    name: 'Z02-Z03',
    location: '中石管网',
    company: '新奥燃气',
    material: '钢铁',
    node: 'P005',
    contact: '19977058561',
  },
])

// 视频监控数据
const videoData = ref([
  {
    id: '1',
    title: '华润燃气东门路口',
    address: '东门路口',
    url: '/mock/stream.webm',
  },
  {
    id: '2',
    title: '兴达街路口东北角',
    address: '兴达街路口东北角',
    url: '/mock/stream2.webm',
  },
  {
    id: '3',
    title: '中燃能源调压站正门',
    address: '中燃能源调压站正门',
    url: '/mock/stream3.webm',
  },
])

// 处置反馈数据
const feedbackData = ref({
  processor: 'xxx',
  submitTime: '年月日时',
  reason: '软硬化',
  processingMethod: '更换缓冲器',
  processingProof: '',
  description: '填写处置情况信息后，点击行行流单',
})

const area = ref([
  [115.91, 39.05],
  [115.92, 39.06],
  [115.93, 39.05],
  [115.91, 39.05],
])

watch(
  () => props.open,
  newVal => {
    if (newVal && mapRef.value && area.value) {
      mapRef.value.drawPolygon(area.value)
    }
  },
)

// 处理更新打开状态的事件
const handleUpdateOpen = (open: boolean) => {
  if (!open) {
    mapRef.value?.destroyPolygon()
    emit('close')
  }
}

// 执行流单
const handleExecuteFlow = () => {
  console.log('执行流单', formData.value)
}

// 地图准备好后的回调
const onMapReady = () => {
  if (area.value) {
    mapRef.value?.drawPolygon(area.value)
  }
}
</script>

<template>
  <Dialog :open="props.open" @update:open="handleUpdateOpen">
    <DialogOverlay class="bg-transparent" />
    <DialogContent
      class="sm:max-w-[1200px] p-0 bg-[#2A384D]/80 rounded-sm border-[#47EBEB] backdrop-blur-sm outline-none max-h-[90vh] overflow-y-auto left-[100%] top-[100%] translate-x-[-102%] translate-y-[-102%]"
    >
      <DialogHeader>
        <DialogTitle class="h-12 text-white pl-9 leading-12 title-bg">施工详情</DialogTitle>
        <DialogDescription as="div" class="p-6 max-h-[820px] overflow-auto text-sm text-white custom-scrollbar">
          <!-- 施工详情和周边管线 -->
          <div class="grid grid-cols-2 gap-8 mb-8">
            <!-- 施工详情 -->
            <div class="space-y-4">
              <h3 class="text-lg font-bold text-white">施工详情</h3>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">项目名称：</span>
                  <span class="flex-1 ml-2">{{ constructionData.projectName }}</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">项目地址：</span>
                  <span class="flex-1 ml-2">雄县雄安庄村</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">备案时间：</span>
                  <span class="flex-1 ml-2">{{ constructionData.contractTime }}</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">开工时间：</span>
                  <span class="flex-1 ml-2">2025年6月15日</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">完工时间：</span>
                  <span class="flex-1 ml-2">{{ constructionData.completionTime }}</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">建设单位：</span>
                  <span class="flex-1 ml-2">{{ constructionData.constructionUnit }}</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">施工单位：</span>
                  <span class="flex-1 ml-2">雄大土木工程</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">单位联系人：</span>
                  <span class="flex-1 ml-2">{{ constructionData.constructionManager }}</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">单位联系人：</span>
                  <span class="flex-1 ml-2">熊达</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">单位联系电话：</span>
                  <span class="flex-1 ml-2">{{ constructionData.unitContact }}</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">单位联系电话：</span>
                  <span class="flex-1 ml-2">{{ constructionData.unitManagerContact }}</span>
                </div>
                <div class="flex col-span-2">
                  <span class="w-28 text-[#99D5FF] text-right">施工区域：</span>
                  <span class="flex-1 ml-2">{{ constructionData.coordinates }}</span>
                </div>
                <div class="flex">
                  <span class="w-28 text-[#99D5FF] text-right">下挖土方：</span>
                  <span class="flex-1 ml-2">{{ constructionData.excavationDepth }}</span>
                </div>
              </div>
            </div>

            <!-- 周边管线 -->
            <div class="space-y-4">
              <h3 class="text-lg font-bold text-white">周边管线</h3>
              <div class="space-y-3">
                <div v-for="(pipeline, index) in pipelineData" :key="index" class="space-y-2 text-sm">
                  <div class="grid grid-cols-2 gap-2">
                    <div class="flex">
                      <span class="w-28 text-[#99D5FF] text-right">管段名称：</span>
                      <span class="flex-1 ml-2">{{ pipeline.name }}</span>
                    </div>
                    <div class="flex">
                      <span class="w-28 text-[#99D5FF] text-right">管网位置：</span>
                      <span class="flex-1 ml-2">{{ pipeline.location }}</span>
                    </div>
                    <div class="flex">
                      <span class="w-28 text-[#99D5FF] text-right">所属企业：</span>
                      <span class="flex-1 ml-2">{{ pipeline.company }}</span>
                    </div>
                    <div class="flex">
                      <span class="w-28 text-[#99D5FF] text-right">管网材质：</span>
                      <span class="flex-1 ml-2">{{ pipeline.material }}</span>
                    </div>
                    <div class="flex">
                      <span class="w-28 text-[#99D5FF] text-right">管网节点：</span>
                      <span class="flex-1 ml-2">{{ pipeline.node }}</span>
                    </div>
                    <div class="flex">
                      <span class="w-28 text-[#99D5FF] text-right">负责人电话：</span>
                      <span class="flex-1 ml-2">{{ pipeline.contact }}</span>
                    </div>
                  </div>
                  <div v-if="index < pipelineData.length - 1" class="border-b border-[#99D5FF]/20 pb-2"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 地图区域 -->
          <div class="flex mb-8">
            <div class="w-full h-[240px] bg-black/20 rounded">
              <Map ref="mapRef" :zoom="14" :pitch="20" @map-ready="onMapReady" />
            </div>
            <!-- <div class="w-[576px] h-[240px] bg-black/20 rounded"></div> -->
          </div>

          <!-- 视频监控 -->
          <div class="mb-8">
            <h3 class="mb-4 text-lg font-bold text-white">视频监控</h3>
            <div class="grid grid-cols-3 gap-6">
              <div v-for="video in videoData" :key="video.id" class="relative">
                <video
                  class="object-fill w-full h-full"
                  :src="video.url"
                  disablepictureinpicture
                  muted
                  loop
                  autoplay
                  playsinline
                ></video>
              </div>
            </div>
          </div>

          <!-- 处置流单 -->
          <div class="mb-8">
            <h3 class="mb-4 text-lg font-bold text-white">处置流单</h3>
            <div class="grid grid-cols-4 gap-4 mb-4">
              <div class="flex items-center gap-2">
                <span class="text-[#99D5FF] whitespace-nowrap">处置人：</span>
                <Select v-model="formData.processor">
                  <SelectTrigger class="flex-1 h-8 text-sm dropdown-btn">
                    <SelectValue placeholder="请选择" />
                  </SelectTrigger>
                  <SelectContent class="text-[#99D5FF]">
                    <SelectGroup>
                      <SelectItem value="user1">张三</SelectItem>
                      <SelectItem value="user2">李四</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              <div class="flex items-center gap-2">
                <span class="text-[#99D5FF] whitespace-nowrap">工单时间：</span>
                <Select v-model="formData.workTime">
                  <SelectTrigger class="flex-1 h-8 text-sm dropdown-btn">
                    <SelectValue placeholder="请选择" />
                  </SelectTrigger>
                  <SelectContent class="text-[#99D5FF]">
                    <SelectGroup>
                      <SelectItem value="today">今天</SelectItem>
                      <SelectItem value="tomorrow">明天</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              <div class="flex items-center gap-2">
                <span class="text-[#99D5FF] whitespace-nowrap">派单原因：</span>
                <Select v-model="formData.reason">
                  <SelectTrigger class="flex-1 h-8 text-sm dropdown-btn">
                    <SelectValue placeholder="请选择" />
                  </SelectTrigger>
                  <SelectContent class="text-[#99D5FF]">
                    <SelectGroup>
                      <SelectItem value="reason1">施工监管</SelectItem>
                      <SelectItem value="reason2">安全检查</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>

              <div class="flex items-center gap-2">
                <span class="text-[#99D5FF] whitespace-nowrap">备注：</span>
                <input
                  v-model="formData.remark"
                  placeholder="请输入..."
                  class="flex-1 h-8 text-sm bg-transparent border border-[#99D5FF] text-white placeholder:text-[#99D5FF]/50 px-2 rounded outline-none focus:border-[#47EBEB]"
                />
              </div>
            </div>

            <div class="flex justify-center mb-6">
              <Button
                class="bg-[#77A8D9] rounded-sm hover:bg-[#77A8D9]/80 text-white outline-none px-8"
                @click="handleExecuteFlow"
              >
                执行流单
              </Button>
            </div>
          </div>

          <!-- 处置反馈 -->
          <div>
            <div class="mb-4 text-lg text-[#99D5FF] font-bold">处置反馈</div>
            <div class="grid grid-cols-4 gap-4">
              <div class="flex">
                <div class="text-[#99D5FF]">处置人：</div>
                <div>{{ feedbackData.processor }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">提交时间：</div>
                <div>{{ feedbackData.submitTime }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">告警原因：</div>
                <div>{{ feedbackData.reason }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">描述：</div>
                <div>{{ feedbackData.description }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF]">处置方式：</div>
                <div>{{ feedbackData.processingMethod }}</div>
              </div>
              <div class="flex">
                <div class="text-[#99D5FF] whitespace-nowrap">处置取证：</div>
                <div>
                  <div class="flex gap-2">
                    <img
                      src="https://img1.baidu.com/it/u=475744281,3916313639&fm=253&app=138&f=JPEG?w=800&h=1067"
                      alt="处置图片"
                      class="rounded w-30 h-30"
                    />
                    <img
                      src="https://pic.rmb.bdstatic.com/bjh/3f13695da2e/250422/b7919057ce453d11734d024ac413fce7.jpeg"
                      alt="处置图片"
                      class="object-cover rounded w-30 h-30"
                    />
                    <img
                      src="https://img0.baidu.com/it/u=671753217,4078023515&fm=253&app=138&f=JPEG?w=800&h=1062"
                      alt="处置图片"
                      class="object-cover rounded w-30 h-30"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogDescription>
      </DialogHeader>
    </DialogContent>
  </Dialog>
</template>

<style scoped>
.title-bg {
  background: url('@/assets/dialog/title-bg-1200.png') no-repeat 0 0;
  background-size: cover;
  font-family: MStiffHei PRC;
}

.dropdown-btn {
  border: 1px solid #99d5ff;
  color: #99d5ff;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  outline: none;
}
</style>
