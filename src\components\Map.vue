<template>
  <div class="map-container">
    <div id="contain"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import type {
  AMapMap,
  AMapMarker,
  AMapPolygon,
  AMapPolyline,
  AMapInstance,
  AMapInfoWindow,
  LocaContainerInstance,
  LocaHeatMapLayer,
  LocaLoadedInstance,
  HeatDataItem,
  Position,
} from '@/types/mapTypes'

// 定义通用的配置接口
interface GenericMarkerConfig {
  id: string | number
  position: Position
  iconUrl?: string // 自定义图标URL
  content?: string // 自定义标记的HTML内容
  extData?: any
  getInfoWindowContent?: (data: any) => string // 用于生成信息窗口内容的函数
}

interface GenericPolylineConfig {
  id: string | number
  path: Position[]
  strokeColor?: string
  strokeWeight?: number
  strokeOpacity?: number
  extData?: any
  getInfoWindowContent?: (data: any) => string
}

const props = defineProps({
  center: {
    type: Array,
    default: () => [114.8, 36.55],
  },
  zoom: {
    type: Number,
    default: 12,
  },
  viewMode: {
    type: String,
    default: '3D',
  },
  pitch: {
    type: Number,
    default: 0,
  },
  clickable: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['map-click', 'map-ready'])

// 响应式状态
const map = ref<AMapMap | null>(null)
const locaContainer = ref<LocaContainerInstance | null>(null)
const AMap = ref<AMapInstance | null>(null)
const Loca = ref<LocaLoadedInstance | null>(null)
const autoTourTimer = ref<NodeJS.Timeout | null>(null)
const currentMarker = ref<AMapMarker | null>(null) // 重命名，避免与通用标记混淆
const polygon = ref<AMapPolygon | null>(null)
const infoWindow = ref<AMapInfoWindow | null>(null)

const heatmapLayer = ref<LocaHeatMapLayer | null>(null)
const outLayer = ref<AMapPolygon | null>(null)

// 存储所有通用图层元素
const allMapLayers = ref<Map<string, (AMapMarker | AMapPolyline | AMapPolygon)[]>>(new Map())

// 初始化地图
const initMap = async () => {
  try {
    await AMapLoader.load({
      key: '8a6865ecdde60542806eb4dd4da77aed',
      version: '2.0',
      plugins: ['AMap.GeoJSON', 'AMap.InfoWindow'],
      Loca: { version: '2.0.0' },
    })

    // 从全局 window 对象获取 AMap 和 Loca 实例
    AMap.value = (window as any).AMap as AMapInstance
    Loca.value = (window as any).Loca as LocaLoadedInstance

    if (!AMap.value) {
      console.error('AMap is not available after loading.')
      return
    }

    map.value = new AMap.value.Map('contain', {
      zoom: props.zoom,
      viewMode: props.viewMode,
      pitch: props.pitch,
      rotation: 0,
      center: props.center,
      mapStyle: 'amap://styles/darkblue',
      skyColor: '#081245',
      willReadFrequently: true,
      dragEnable: true,
      zoomEnable: true,
      doubleClickZoom: true,
      keyboardEnable: true,
    })

    // 在地图加载完成后再创建 Loca 容器
    map.value.on('complete', () => {
      if (Loca.value && map.value) {
        locaContainer.value = new Loca.value.Container({ map: map.value })
        if (locaContainer.value) {
          locaContainer.value.pointLight.intensity = 0
          locaContainer.value.ambLight.intensity = 1
          locaContainer.value.animate.start()
          // 显示区域图层
          showDistrictLayer()
        }
      }

      // 适应边界范围
      if (map.value && outLayer.value) {
        map.value.setFitView([outLayer.value], false, [20, 20, 20, 20])
      }

      emit('map-ready')
    })

    if (props.clickable) {
      map.value.on('click', e => {
        // 清除上一个标记
        if (currentMarker.value) {
          map.value!.remove(currentMarker.value)
        }
        // 创建新的标记
        currentMarker.value = new AMap.value!.Marker({
          position: e.lnglat,
        })
        map.value!.add(currentMarker.value)
        // 发送事件
        emit('map-click', e.lnglat)
      })
    }
  } catch (error) {
    console.error('地图加载失败:', error)
  }
}
// 显示区域图层
const showDistrictLayer = () => {
  if (!locaContainer.value || !Loca.value) {
    console.error('Loca or AMap is not initialized.')
    return
  }
  const outGeo = new Loca.value.GeoJSONSource({
    url: '/geo/feixiang-geometry.geojson',
  })

  const outLayer = new Loca.value.PolygonLayer({
    zIndex: 1,
    cullface: 'none',
    shininess: 1,
    acceptLight: false,
    blockHide: false,
    hasSide: true,
    hasTop: true,
    hasBottom: false,
    depth: true,
  })
  outLayer.setSource(outGeo)
  outLayer.setStyle({
    sideTopColor: function () {
      return 'rgba(30, 96, 174, 0.2)'
    },
    topColor: function () {
      return 'rgba(30, 96, 174, 0.2)'
    },
    sideBottomColor: function () {
      return 'rgba(45, 206, 252, 1)'
    },
    height: 1000,
    altitude: 0,
  })
  locaContainer.value.add(outLayer)
}

// 显示热力图
const showHeatMap = (heatData: HeatDataItem[]) => {
  destroyHeatMap() // 先移除已有的

  if (!heatData || heatData.length === 0) {
    return
  }

  if (!locaContainer.value || !Loca.value) {
    console.error('Loca or AMap is not initialized.')
    return
  }

  const heatGeo = new Loca.value.GeoJSONSource({
    data: {
      type: 'FeatureCollection',
      features: heatData.map(item => ({
        type: 'Feature',
        properties: { num: item.num },
        geometry: {
          type: 'Point',
          coordinates: item.lnglat,
        },
      })),
    },
  })

  heatmapLayer.value = new Loca.value.HeatMapLayer({
    zIndex: 10,
    opacity: 1,
    visible: true,
    zooms: [2, 22],
  })

  heatmapLayer.value.setSource(heatGeo, {
    radius: 30, // 半径
    unit: 'meter',
    height: 100, // 高度
    gradient: {
      0.1: '#2A85B8',
      0.2: '#16B0A9',
      0.3: '#29CF6F',
      0.4: '#5CE182',
      0.5: '#7DF675',
      0.6: '#FFF100',
      0.7: '#FAA53F',
      1: '#D04343',
    },
    value: (_index: number, feature: any) => feature.properties.num,
    heightBezier: [0, 0.53, 0.37, 0.98],
  })

  locaContainer.value.add(heatmapLayer.value)
}

// 销毁热力图
const destroyHeatMap = () => {
  if (heatmapLayer.value && locaContainer.value) {
    locaContainer.value.remove(heatmapLayer.value)
    heatmapLayer.value = null
  }
}

const addMarker = (position: Position) => {
  if (!map.value || !AMap.value) {
    console.error('Map or AMap is not initialized.')
    return
  }
  if (currentMarker.value) {
    map.value.remove(currentMarker.value)
  }
  currentMarker.value = new AMap.value.Marker({
    position: [position.lng, position.lat],
  })
  map.value.add(currentMarker.value)
}

const destroyMarker = () => {
  if (currentMarker.value && map.value) {
    map.value.remove(currentMarker.value)
    currentMarker.value = null
  }
}

// 绘制多边形
const drawPolygon = (path: any[]) => {
  destroyPolygon()
  if (!map.value || !AMap.value) {
    console.error('Map or AMap is not initialized.')
    return
  }
  polygon.value = new AMap.value.Polygon({
    path: path,
    strokeColor: '#00FF00',
    strokeWeight: 2,
    strokeOpacity: 0.8,
    fillColor: '#00FF00',
    fillOpacity: 0.3,
    zIndex: 50,
  })
  map.value.add(polygon.value)
  map.value.setFitView([polygon.value])
}

// 销毁多边形
const destroyPolygon = () => {
  if (polygon.value && map.value) {
    map.value.remove(polygon.value)
    polygon.value = null
  }
}

// 通用添加标记方法
const addGenericMarkers = (layerId: string, markersConfig: GenericMarkerConfig[]) => {
  if (!map.value || !AMap.value) {
    console.error('Map or AMap is not initialized.')
    return
  }
  clearLayer(layerId) // 先清除该图层已有的元素

  const markers: AMapMarker[] = []
  markersConfig.forEach(config => {
    const marker = new AMap.value!.Marker({
      position: [config.position.lng, config.position.lat],
      offset: new AMap.value!.Pixel(-12, -24),
      extData: config.extData,
      content:
        config.content || (config.iconUrl ? `<img src="${config.iconUrl}" style="width:24px;height:24px;">` : ''),
    })

    if (config.getInfoWindowContent) {
      marker.on('mouseover', (e: any) => {
        ;(e.target as AMapMarker).setTop(true)
        const infoObj = (e.target as AMapMarker).getExtData()
        const content = config.getInfoWindowContent!(infoObj)
        if (infoWindow.value) {
          infoWindow.value.close()
        }
        infoWindow.value = new AMap.value!.InfoWindow({
          content: content,
          offset: new AMap.value!.Pixel(0, -30),
          isCustom: true,
          closeWhenClickMap: false,
        })
        infoWindow.value.open(map.value!, e.lnglat)
      })

      marker.on('mouseout', e => {
        ;(e.target as AMapMarker).setTop(false)
        if (infoWindow.value) {
          infoWindow.value.close()
        }
      })
    }

    map.value!.add(marker)
    markers.push(marker)
  })
  allMapLayers.value.set(layerId, markers)
}

// 通用添加管线方法
const addGenericPolylines = (layerId: string, polylinesConfig: GenericPolylineConfig[]) => {
  if (!map.value || !AMap.value) {
    console.error('Map or AMap is not initialized.')
    return
  }
  clearLayer(layerId)

  const polylines: AMapPolyline[] = []
  polylinesConfig.forEach(config => {
    const polyline = new AMap.value!.Polyline({
      path: config.path.map(p => [p.lng, p.lat]),
      strokeColor: config.strokeColor || '#FF0000',
      strokeWeight: config.strokeWeight || 3,
      strokeOpacity: config.strokeOpacity || 0.8,
      extData: config.extData,
    })

    if (config.getInfoWindowContent) {
      polyline.on('mouseover', (e: any) => {
        e.target.setOptions({ strokeOpacity: 1, isOutline: true })
        const infoObj = polyline.getExtData()
        const content = config.getInfoWindowContent!(infoObj)
        if (infoWindow.value) {
          infoWindow.value.close()
        }
        infoWindow.value = new AMap.value!.InfoWindow({
          content: content,
          offset: new AMap.value!.Pixel(0, -30),
          isCustom: true,
          closeWhenClickMap: false,
        })
        infoWindow.value.open(map.value!, e.lnglat)
      })

      polyline.on('mouseout', e => {
        e.target.setOptions({ strokeOpacity: 0.8, isOutline: false })
        if (infoWindow.value) {
          infoWindow.value.close()
        }
      })
    }

    map.value!.add(polyline)
    polylines.push(polyline)
  })
  allMapLayers.value.set(layerId, polylines)
}

// 通用清除图层方法
const clearLayer = (layerId: string) => {
  if (!map.value) return
  const elements = allMapLayers.value.get(layerId)
  if (elements && elements.length > 0) {
    map.value.remove(elements)
    allMapLayers.value.delete(layerId)
  }
  if (infoWindow.value) {
    infoWindow.value.close()
  }
}

// 生命周期钩子
onMounted(initMap)

defineExpose({
  showHeatMap,
  destroyHeatMap,
  addMarker,
  destroyMarker,
  drawPolygon,
  destroyPolygon,
  addGenericMarkers,
  addGenericPolylines,
  clearLayer,
  getAMapInstance: () => AMap.value, // 暴露 AMap 实例，以便父组件创建复杂元素
})

onBeforeUnmount(() => {
  if (locaContainer.value) locaContainer.value.destroy()
  if (map.value) map.value.destroy()
  if (autoTourTimer.value) clearTimeout(autoTourTimer.value)
  if (infoWindow.value) infoWindow.value.close()
  allMapLayers.value.clear() // 清除所有图层引用
})
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#contain {
  width: 100%;
  height: 100%;
}
</style>
