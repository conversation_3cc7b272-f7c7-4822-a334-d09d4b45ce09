<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">入户类型分布</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 左侧统计卡片区域 -->
        <div class="user-overview-container">
          <div v-for="(item, index) in userStats" :key="index" class="user-card">
            <div class="card-content">
              <div class="card-icon">
                <img :src="item.icon" alt="" />
                <div v-if="item.secondIcon" class="second-icon">
                  <img :src="item.secondIcon" alt="" />
                </div>
              </div>
              <div class="card-info">
                <div class="card-title">{{ item.title }}</div>
                <div class="card-value-container">
                  <div class="card-value">{{ item.value }}</div>
                  <div class="card-unit">{{ item.unit }}</div>
                </div>
              </div>
            </div>

            <!-- 移除两侧竖线图片，改为背景图 -->
          </div>
        </div>
        <!-- 右侧图表区域 -->
        <div class="chart-panel">
          <div class="chart-content">
            <div ref="chartRef" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

interface UserStat {
  type: string
  title: string
  value: string
  icon: string
  secondIcon?: string // 添加可选的 secondIcon 属性
  unit?: string // 添加可选的 unit 属性
}

// 图标数据配置 - 使用图片路径
const iconData = {
  dwUser: '/src/assets/indoor-icon/dw-user.svg',
  czUser: '/src/assets/indoor-icon/cz-user.svg',
  ncUser: '/src/assets/indoor-icon/nc-user.svg',
  iconDown: '/src/assets/indoor-icon/user-bg.svg',
  union: '/src/assets/industry-icons/Union.svg',
}

const chartRef = ref<HTMLElement>()

let chart: echarts.ECharts | null = null
// Mock数据

const option = {
  backgroundColor: 'transparent',
  title: {
    left: '39%',
    top: '38%',
    textAlign: 'center',
    text: '材质分析',
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    subtext: '116.2',
    subtextStyle: {
      color: '#fff',
      fontSize: 24,
    },
  },
  legend: {
    show: true,
    orient: 'vertical',
    right: '10%',
    bottom: '10%',
    textStyle: {
      color: '#fff',
      fontSize: 14,
    },
    itemWidth: 12,
    itemHeight: 8,
    icon: 'rect',
  },
  series: [
    {
      type: 'pie',
      radius: ['36.1%', '74.1%'],
      center: ['40%', '50%'],
      silent: true,
      data: [{ value: 1, itemStyle: { color: 'rgba(153, 213, 255, 0.15)' } }],
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
    },
    {
      type: 'pie',
      radius: ['41.7%', '68.5%'],
      center: ['40%', '50%'],
      data: [
        { value: 40, name: '钢管', itemStyle: { color: '#409FFF' } },
        { value: 25, name: '铸铁管', itemStyle: { color: '#66FFFF' } },
        { value: 15, name: '其它', itemStyle: { color: '#FFC61A' } },
        { value: 20, name: '非金属管', itemStyle: { color: '#FF4C4D' } },
      ],
      label: {
        show: true,
        color: '#fff',
        formatter: `{percent|{d}%}\n{value|{c}}个`,
        rich: {
          percent: {
            fontSize: 20,
            color: '#fff',
          },
          value: {
            fontSize: 12,
            color: '#fff',
          },
        },
      },
      labelLine: {
        show: true,
      },
    },
  ],
}

const userStats = ref<UserStat[]>([
  {
    type: 'total',
    title: '单位用户（个）',
    value: '1,245,678',
    icon: iconData.dwUser,
    unit: '个',
  },
  {
    type: 'pipeline',
    title: '城镇居民（户）',
    value: '1,245,678',
    icon: iconData.czUser,
    unit: '户',
  },
  {
    type: 'pipeline',
    title: '农村居民（户）',
    value: '1,245,678',
    icon: iconData.ncUser,
    unit: '户',
  },
])

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .chart-content {
    flex: 1;
    padding: 4px;

    .chart-container {
      width: 100%;
      height: 100%;
    }
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  width: 33.33%;
  flex-shrink: 0;
  height: 100%;
}

.user-card {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
  /* 卡片间距 */
  background: url('/src/assets/indoor-icon/right-bg.svg') no-repeat center/cover;
}
.user-card:not(:last-child) {
  margin-bottom: 16px;
}

.card-content {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  bottom: 12px;
  height: auto;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 12px;
}

.card-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  > img {
    position: absolute;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    z-index: 1;
  }
  .second-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 18px;
    height: 18px;
    z-index: 2;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-info {
  flex: 1;
  min-width: 0;
  height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  .card-title {
    width: 100%;
    height: 18px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC';
    font-size: 13px;
    line-height: 18px;
    text-align: left;
    margin-bottom: 2px;
  }
  .card-value-container {
    width: 100%;
    height: 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: baseline;
    .card-value {
      color: #ffffff;
      font-family: 'DINPro';
      font-size: 16px;
      line-height: 20px;
      font-weight: bold;
      margin-right: 2px;
    }
    .card-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 12px;
      line-height: 16px;
    }
  }
}

.card-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;

  > img {
    position: absolute;
    top: 0;
    left: 0;
    width: 48px;
    height: 48px;
    z-index: 1;
  }

  .second-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    z-index: 2;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-info {
  flex: 1;
  height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  .card-title {
    width: 100%;
    height: 20px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC';
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    margin-bottom: 4px;
  }

  .card-value-container {
    width: 100%;
    height: 24px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: baseline;

    .card-value {
      color: #ffffff;
      font-family: 'DINPro';
      font-size: 18px;
      line-height: 24px;
      font-weight: bold;
      margin-right: 4px;
    }

    .card-unit {
      color: #ffffff;
      font-family: 'Noto Sans SC';
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.card-border-bottom {
  position: absolute;
  left: 30px;
  bottom: 0px;
  width: calc(100% - 60px);
  height: 12px;
}
</style>
