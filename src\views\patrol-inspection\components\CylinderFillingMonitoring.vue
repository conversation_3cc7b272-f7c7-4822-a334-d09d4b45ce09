<template>
  <div class="panel-container">
    <div class="panel-header">未整改隐患预警</div>
    <div class="warning-list-outer" :style="{ height: `${visibleCount * rowHeight}px` }" ref="listContainerRef">
      <div
        class="warning-list"
        :style="{
          transform: `translateY(-${scrollTop}px)`,
          transition: transitionEnabled ? 'transform 0.5s ease' : 'none',
        }"
      >
        <div
          v-for="item in scrollList"
          :key="item.id + '-' + Math.random()"
          class="warning-row"
          :style="{ backgroundImage: `url(/src/assets/patrol-inspection/${item.n}.svg)` }"
        >
          <span class="warning-title">中心区域管道锈蚀-编号{{ item.code }}</span>
          <span class="warning-overdue">超期{{ item.n }}天</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

function getRandomInt(min: number, max: number) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}
function shuffle<T>(arr: T[]): T[] {
  return arr
    .map(item => ({ item, sort: Math.random() }))
    .sort((a, b) => a.sort - b.sort)
    .map(({ item }) => item)
}

const total = 20
const warningData = ref(
  shuffle(
    Array.from({ length: total }, (_, i) => ({
      id: i + 1,
      code: (getRandomInt(1, 999) + '').padStart(3, '0'),
      n: getRandomInt(1, 5),
    })),
  ),
)

const rowHeight = 50
const visibleCount = 5
const scrollList = computed(() => [...warningData.value, ...warningData.value])
const listContainerRef = ref<HTMLElement | null>(null)
const scrollTimer = ref<any>(null)
const scrollTop = ref(0)
const transitionEnabled = ref(true)

const startScrolling = () => {
  stopScrolling()
  scrollTimer.value = setInterval(() => {
    scrollTop.value += rowHeight
    if (scrollTop.value >= warningData.value.length * rowHeight) {
      transitionEnabled.value = false
      scrollTop.value = 0
      setTimeout(() => {
        transitionEnabled.value = true
      }, 50)
    }
  }, 2000)
}

const stopScrolling = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value)
    scrollTimer.value = null
  }
}

onMounted(() => {
  startScrolling()
  const container = listContainerRef.value
  if (container) {
    container.addEventListener('mouseenter', stopScrolling)
    container.addEventListener('mouseleave', startScrolling)
  }
})
onUnmounted(() => {
  stopScrolling()
  const container = listContainerRef.value
  if (container) {
    container.removeEventListener('mouseenter', stopScrolling)
    container.removeEventListener('mouseleave', startScrolling)
  }
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.warning-list-outer {
  overflow: hidden;
  padding: 6px 0;
}
.warning-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.warning-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 24px;
  border-radius: 8px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: #fff;
  font-family: 'Noto Sans SC', sans-serif;
  font-size: 14px;
  font-weight: 500;
}
.warning-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.warning-overdue {
  margin-left: 16px;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
}
</style>
