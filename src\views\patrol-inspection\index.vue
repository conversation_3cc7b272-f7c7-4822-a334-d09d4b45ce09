<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <IndustryOverview />
      <SupervisionInspection />
      <HouseholdInspection />
    </div>

    <!-- 中央地图区域 -->
    <div class="center-panel">
      <!-- 地图控制组件 -->
      <LayersTool v-model="layersData" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <RiskHazardMonitoring />
      <OldPipeNetworkRenovation />
      <CylinderFillingMonitoring />
    </div>

    <!-- 弹窗 -->
    <IndustryInfoDialog :open="industryInfoOpen" @close="industryInfoOpen = false" />
    <StationDialog :open="stationInfoOpen" @close="stationInfoOpen = false" />
    <PipelineDialog :open="pipelineInfoOpen" @close="pipelineInfoOpen = false" />
    <AreaDialog :open="areaInfoOpen" @close="areaInfoOpen = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import IndustryOverview from './components/IndustryOverview.vue'
import HouseholdInspection from './components/HouseholdInspection.vue'
import SupervisionInspection from './components/SupervisionInspection.vue'
import RiskHazardMonitoring from './components/RiskHazardMonitoring.vue'
import OldPipeNetworkRenovation from './components/OldPipeNetworkRenovation.vue'
import CylinderFillingMonitoring from './components/CylinderFillingMonitoring.vue'
import LayersTool from '@/components/LayersTool.vue'
import IndustryInfoDialog from '@/components/ue-dialog/IndustryInfoDialog.vue'
import StationDialog from '@/components/ue-dialog/StationDialog.vue'
import PipelineDialog from '@/components/ue-dialog/PipelineDialog.vue'
import AreaDialog from '@/components/ue-dialog/AreaDialog.vue'
import { layerData } from './layerData'

// 弹窗状态

const industryInfoOpen = ref(false)
const stationInfoOpen = ref(false)
const pipelineInfoOpen = ref(false)
const areaInfoOpen = ref(false)

// 图层数据
const layersData = ref(layerData)

// 交互处理

onMounted(() => {
  // todo
})

onUnmounted(() => {
  // 清除所有图层
})
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 96px;
  left: 24px;
  right: 24px;
  height: calc(100% - 96px);
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 744px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.left-panel {
  left: 0;
}

.right-panel {
  right: 0;
}

.center-panel {
  position: absolute;
  top: 0;
  left: 768px;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 300px 1fr 300px;
    gap: 20px;
    padding: 20px;
    padding-top: 120px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 280px 1fr 280px;
    gap: 16px;
    padding: 16px;
    padding-top: 120px;
  }
}
</style>
