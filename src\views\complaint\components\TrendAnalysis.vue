<template>
  <div class="panel-container">
    <div class="panel-header">投诉趋势分析</div>
    <div class="panel-content p-2">
      <div ref="container" class="w-full h-full"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, onBeforeUnmount, ref } from 'vue'

const container = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null

const init = () => {
  if (!container.value) return
  chart = echarts.init(container.value)
  const x = Array.from({ length: 7 }).map((_, i) => `11-0${i + 1}`)
  const y = [60, 80, 90, 70, 50, 85, 55]
  chart.setOption({
    grid: { left: 30, right: 20, top: 20, bottom: 26 },
    xAxis: {
      type: 'category',
      data: x,
      boundaryGap: false,
      axisLabel: { color: '#66FFFF' },
      axisLine: { lineStyle: { color: 'rgba(102,255,255,.3)' } },
    },
    yAxis: {
      type: 'value',
      axisLabel: { color: '#66FFFF' },
      splitLine: { lineStyle: { color: 'rgba(102,255,255,.15)' } },
    },
    tooltip: { trigger: 'axis' },
    series: [
      {
        type: 'line',
        data: y,
        smooth: true,
        areaStyle: {
          color: new (echarts as any).graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255,217,102,.6)' },
            { offset: 1, color: 'rgba(255,217,102,0)' },
          ]),
        },
        lineStyle: { color: '#FFD966' },
        showSymbol: false,
      },
    ],
  })
}

onMounted(() => init())
onBeforeUnmount(() => chart?.dispose())
</script>

<style scoped>
@import '@/styles/index.css';
</style>
