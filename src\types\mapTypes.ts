// Define basic types for AMap and Loca objects based on usage
export interface AMapMap {
  // on(event: 'complete' | 'click', handler: (e: { lnglat: { lng: number; lat: number } }) => void): void
  on(event: string, handler: (e: any) => void): void
  remove(overlay: any): void
  add(overlay: any): void
  setFitView(overlays: any[], animate?: boolean, padding?: number[]): void
  destroy(): void
}

export interface AMapMarker {
  on(event: 'mouseover' | 'mouseout', handler: (e: { target: AMapMarker }) => void): void
  setTop(isTop: boolean): void
  getExtData(): any
  dom: HTMLElement
}

export interface AMapInfoWindow {
  open(map: AMapMap, position: [number, number]): void
  close(): void
  setContent(content: string | HTMLElement): void
  setPosition(position: [number, number]): void
  // ...其他 AMapInfoWindow 方法
}

export interface AMapPolygon {
  path: any
  strokeColor: string
  strokeWeight: number
  strokeOpacity: number
  fillColor: string
  fillOpacity: number
  zIndex: number
}

export interface AMapPolyline {
  on(eventName: string, handler: (event: any) => void): void
  path: any
  strokeColor: string
  strokeWeight: number
  strokeOpacity: number
  extData: any
  getExtData(): any
}

export interface AMapInstance {
  Map: new (container: string, options: any) => AMapMap
  Polygon: new (options: any) => AMapPolygon
  Marker: new (options: any) => AMapMarker
  Polyline: new (options: any) => AMapPolyline
  Pixel: new (x: number, y: number) => any
  InfoWindow: new (opts?: any) => AMapInfoWindow
  GeoJSON: any
}

export interface LocaContainerInstance {
  pointLight: { intensity: number }
  ambLight: { intensity: number }
  animate: { start: () => void }
  add(layer: any): void
  remove(layer: any): void
  destroy(): void
}

export interface LocaHeatMapLayer {
  setSource(source: any, options: any): void
}

interface LocaGeoJSONSource {
  new (options: any): any
}

export interface LocaLoadedInstance {
  Container: new (options: { map: AMapMap | null }) => LocaContainerInstance
  HeatMapLayer: new (options: any) => LocaHeatMapLayer
  GeoJSONSource: new (options: any) => LocaGeoJSONSource
  PolygonLayer: new (options: any) => any
}

// Combined type for the object returned by AMapLoader.load
export interface AMapLoaderResult {
  AMap: AMapInstance
  Loca: LocaLoadedInstance
}
// Define interface for heatData
export interface HeatDataItem {
  num: number
  lnglat: [number, number]
}

export interface Position {
  lng: number
  lat: number
}

export interface GasCompanyItem {
  geometry: {
    coordinates: [string, string]
  }
  properties: {
    qymc?: string
    fddbrxm?: string
    zzzs?: string
    jyzt?: string
  }
}

export interface StationItem {
  geometry: {
    coordinates: [string, string]
  }
  properties: {
    czmc?: string
    qymc?: string
    fzrmc?: string
    wz?: string
    fzrlxdh?: string
  }
}

export interface PipelineItem {
  geometry: string // JSON string
  properties: any
}
