<template>
  <div class="not-found">
    <div class="content">
      <div class="code">404</div>
      <div class="message">页面未找到</div>
      <div class="back" @click="router.push('/gas')">返回首页</div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router'
const router = useRouter()
</script>
<style scoped>
.not-found {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(180deg, rgba(11, 46, 115, 0.3) 0%, rgba(11, 46, 115, 0.6) 100%);
  backdrop-filter: blur(8px);
}
.content {
  text-align: center;
}
.code {
  font-family: 'MStiffHei PRC';
  font-size: 120px;
  line-height: 1.2;
  background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.6) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.message {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.8);
  margin: 20px 0 40px;
}
.back {
  display: inline-block;
  padding: 12px 32px;
  border-radius: 4px;
  background: rgba(0, 146, 255, 0.2);
  border: 1px solid rgba(0, 146, 255, 0.5);
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}
.back:hover {
  background: rgba(0, 146, 255, 0.3);
}
</style>
