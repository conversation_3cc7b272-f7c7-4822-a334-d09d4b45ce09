<template>
  <div class="panel-container">
    <div class="panel-header">管网资产</div>
    <div>
      <ul class="p-4">
        <li v-for="(row, idx) in indList" :key="idx" class="flex gap-4">
          <p
            v-for="item in row"
            :key="item.id"
            class="flex items-center grow basis-0 h-14 leading-14 box-border border-b border-[#409fff]/30"
          >
            <span class="flex-none text-sm">{{ item.name }}</span>
            <span class="text-[#66FFFF] text-xl text-right grow">{{ item.value }}</span>
            <span class="text-[#66FFFF] text-sm">{{ item.unit }}</span>
          </p>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const indList = ref([
  [
    { name: '管网总长', value: 132.13, unit: 'KM', id: 0 },
    { name: '高压管网', value: 16, unit: 'KM', id: 1 },
    { name: '中压管网', value: 43.66, unit: 'KM', id: 2 },
    { name: '低压管网', value: 72.47, unit: 'KM', id: 3 },
  ],
  [
    { name: '管段数量', value: 185, unit: '段', id: 4 },
    { name: '节点数量', value: 371, unit: '个', id: 5 },
    { name: '城镇管网', value: 63.47, unit: 'KM', id: 6 },
    { name: '农村管网', value: 68.66, unit: 'KM', id: 7 },
  ],
  [
    { name: '管龄≤15年', value: 132.13, unit: 'KM', id: 8 },
    { name: '管龄＞15年', value: 0, unit: 'KM', id: 9 },
  ],
  [
    { name: '管网燃气探测器', value: 86, unit: '台', id: 12 },
    { name: '泄漏报警', value: 0, unit: '台', id: 13 },
    { name: '亏电设备', value: 1, unit: '台', id: 14 },
    { name: '离线设备', value: 2, unit: '台', id: 15 },
  ],
])
</script>
<style scoped>
@import '@/styles/index.css';
</style>
