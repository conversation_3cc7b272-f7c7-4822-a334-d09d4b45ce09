<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">本月重点巡查区域TOP5</div>
      <div class="header-dropdown">
        <div class="btn-group">
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'administrative' }"
            @click="selectedOpt = 'administrative'"
          >
            城镇
          </button>
          <button
            class="period-btn"
            :class="{ active: selectedOpt === 'enterprise' }"
            @click="selectedOpt = 'enterprise'"
          >
            农村
          </button>
        </div>
      </div>
    </div>
    <div class="p-4 panel-content">
      <div class="equipment-alarm-container">
        <!-- 右侧图表区域 -->
        <div class="chart-panel">
          <div class="chart-content">
            <div class="chart-container">
              <img :src="barBg" class="chart-bg-img" alt="bar-bg" />
              <div ref="chartRef" class="chart-echart"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import barBg from '@/assets/patrol-inspection/bar-bg.svg'

const chartRef = ref<HTMLElement>()
const selectedOpt = ref<string>('administrative')
let chart: echarts.ECharts | null = null

let chartData = {
  color: '0,205,151',
  yAxisData: ['xx商业中心', '测试2', '测试3', '测试4', '测试5'],
  data: [20, 16, 18, 24, 26],
}

const option = {
  backgroundColor: 'transparent',
  grid: {
    left: '5%',
    right: '4%',
    bottom: '-20%',
    top: '4%',
    containLabel: true,
  },
  xAxis: [
    {
      show: false,
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: '#11456F',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#0B5EA0',
        },
      },
      axisLabel: {
        margin: 20,
        textStyle: {
          color: 'rgb(183,227,252)',
        },
      },
    },
  ],
  yAxis: [
    {
      type: 'category',
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      data: chartData.yAxisData,
    },
  ],
  tooltip: {
    show: false,
  },
  series: [
    {
      name: '',
      type: 'bar',
      zlevel: 2,
      barWidth: 16,
      label: {
        show: true,
        position: ['0%', '-110%'], // 左上角
        formatter: function (params: any) {
          const name = chartData.yAxisData[params.dataIndex]
          return name
        },
        color: '#fff',
        fontSize: 12,
        fontWeight: 'lighter',
        align: 'left',
      },
      showBackground: true,
      backgroundStyle: {
        color: 'rgba(255, 198, 26, 0.15)',
      },
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(255, 198, 26, 0.45)',
            },
            {
              offset: 1,
              color: 'rgba(255, 198, 26, 1)',
            },
          ],
        },
        borderColor: 'rgba(255, 198, 26, 0.15)',
        borderWidth: 5,
      },
      data: chartData.data,
    },
    {
      name: '',
      type: 'bar',
      zlevel: 3,
      barGap: '-100%',
      barWidth: 16,

      label: {
        show: true,
        position: ['630', '-18'], // 右上角
        formatter: function (params: any) {
          return params.value
        },
        color: '#FFFF',
        fontSize: 12,
        fontWeight: '400',
      },
      itemStyle: {
        color: 'rgba(0,0,0,0)', // 透明，不显示柱体
      },
      data: chartData.data,
    },
    {
      name: '',
      type: 'pictorialBar',
      itemStyle: {
        color: '#FFC61A',
      },
      symbol: 'rect',
      symbolSize: [3.5, 16.5],
      symbolPosition: 'end',
      zlevel: 3,
      data: chartData.data,
    },
  ],
}

const initChart = () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.equipment-alarm-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .chart-content {
    flex: 1;
    padding: 2px;

    .chart-container {
      width: 100%;
      height: 220px;
      min-height: 220px;
      max-height: 220px;
      padding: 0;
      margin: 0;
      position: relative;
      overflow: hidden;

      .chart-bg-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
        pointer-events: none;
      }

      .chart-echart {
        position: relative;
        width: 100%;
        height: 100%;
        z-index: 2;
      }
    }
  }
}

.user-overview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 33.33%;
  flex-shrink: 0;
}

.user-card {
  position: relative;
  width: 100%;
  height: 104px;
  overflow: hidden;

  .card-content {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    height: 64px;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    gap: 16px;

    .card-icon {
      flex-shrink: 0;
      width: 48px;
      height: 48px;
      display: flex;
      position: relative;
      align-items: center;
      justify-content: center;

      > img {
        position: absolute;
        top: 0;
        left: 0;
        width: 48px;
        height: 48px;
        z-index: 1;
      }

      .second-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        z-index: 2;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .card-info {
      flex: 1;
      height: 48px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;

      .card-title {
        width: 100%;
        height: 20px;
        white-space: nowrap;
        color: #66ffff;
        font-family: 'Noto Sans SC';
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        margin-bottom: 4px;
      }

      .card-value-container {
        width: 100%;
        height: 24px;
        display: flex;
        flex-direction: row;
        justify-content: start;
        align-items: baseline;

        .card-value {
          color: #ffffff;
          font-family: 'DINPro';
          font-size: 18px;
          line-height: 24px;
          font-weight: bold;
          margin-right: 4px;
        }

        .card-unit {
          color: #ffffff;
          font-family: 'Noto Sans SC';
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
  }

  .card-border-bottom {
    position: absolute;
    left: 30px;
    bottom: 0px;
    width: calc(100% - 60px);
    height: 12px;
  }

  .card-side-lines {
    .side-line {
      position: absolute;
      top: 0px;
      width: 5.5px;
      height: 115px;

      &.left {
        left: 0px;
      }

      &.right {
        right: 0px;
      }
    }
  }
}
.btn-group {
  display: flex;
  gap: 8px;
}

.period-btn {
  border: 1px solid #409fff;
  background: rgba(64, 159, 255, 0.15);
  color: #fff;
  font-size: 12px;
  font-weight: 300;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  border-radius: 4px;
  text-align: center;
  height: 28px;
  line-height: 0px;
}

.period-btn:hover {
  background: rgba(74, 158, 255, 0.1);
}
.period-btn.active {
  background: rgba(255, 198, 26, 0.15);
  color: #fff;
  border-color: #ffd700;
}
</style>
