<template>
  <div class="dashboard-layout">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <VehicleOverview />
      <AbnormalParkingTable class="mt-6" />
    </div>

    <div class="center-panel-left">
      <LayersTool v-model="layersData" />
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <div class="right-row">
        <CompanyServiceOverview />
        <RegionVehicleRanking />
      </div>
      <VehicleTracking class="mt-6" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import LayersTool from '@/components/LayersTool.vue'
import VehicleOverview from './components/VehicleOverview.vue'
import AbnormalParkingTable from './components/AbnormalParkingTable.vue'
import CompanyServiceOverview from './components/CompanyServiceOverview.vue'
import RegionVehicleRanking from './components/RegionVehicleRanking.vue'
import VehicleTracking from './components/VehicleTracking.vue'
import { layerData } from './layerData'

const layersData = ref(layerData)
</script>

<style scoped>
.dashboard-layout {
  position: absolute;
  top: 96px;
  left: 24px;
  right: 24px;
  height: calc(100% - 96px);
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: absolute;
  top: 0;
  width: 744px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  z-index: 10;
}

.left-panel {
  left: 0;
}
.right-panel {
  right: 0;
}

.center-panel-left {
  position: absolute;
  top: 0;
  left: 768px;
  z-index: 10;
}

.right-row {
  display: flex;
  gap: 24px;
}
</style>
