<template>
  <div class="panel-container">
    <div class="panel-header">企业概况</div>
    <div class="industry-grid">
      <div v-for="item in industryData" :key="item.id" class="industry-card">
        <!-- 数值区域 -->
        <div class="card-value-section">
          <div class="value-number">{{ item.value }}</div>
          <div class="value-unit">{{ item.unit }}</div>
        </div>

        <!-- 图标和标题区域 -->
        <div class="card-content-section">
          <div class="icon-container">
            <!-- 背景装饰星形 -->
            <img class="star-bg-large" src="/src/assets/industry-icons/Star2.svg" alt="" />
            <img class="star-bg-small" src="/src/assets/industry-icons/Star3.svg" alt="" />
            <div class="main-icon">
              <img :src="item.icon" :alt="item.name" />
            </div>
          </div>
          <div class="card-title">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'

const industryData = ref([
  {
    id: 1,
    name: '企业总量',
    value: 86,
    unit: '家',
    icon: '/src/assets/industry-icons/enterprise-total.svg',
  },
  {
    id: 2,
    name: '管道燃气企业',
    value: 86,
    unit: '家',
    icon: '/src/assets/industry-icons/pipeline-gas.svg',
  },
  {
    id: 3,
    name: '瓶装燃气企业',
    value: 86,
    unit: '家',
    icon: '/src/assets/industry-icons/bottled-gas.svg',
  },
  {
    id: 4,
    name: '加气站企业',
    value: 86,
    unit: '家',
    icon: '/src/assets/industry-icons/gas-station.svg',
  },
])
</script>
<style lang="scss" scoped>
@import '@/styles/index.css';

.industry-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 20px;
}

.industry-card {
  width: 348px;
  height: 104px;
  background: linear-gradient(
    270deg,
    rgba(64, 159, 255, 0) 0%,
    rgba(64, 159, 255, 0.15) 50%,
    rgba(64, 159, 255, 0) 100%
  );
  border: 1px solid;
  border-image: linear-gradient(0deg, rgba(64, 159, 255, 0) 0%, rgba(64, 159, 255, 0.6) 50%, rgba(64, 159, 255, 0) 100%)
    1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  overflow: hidden;
}

.card-value-section {
  position: absolute;
  top: 36px;
  right: 24px;
  width: 45px;
  height: 32px;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;

  .value-number {
    flex-shrink: 0;
    width: 29px;
    height: 32px;
    white-space: nowrap;
    color: #66ffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 24px;
    line-height: 32px;
    text-align: right;
    font-weight: bold;
  }

  .value-unit {
    flex-shrink: 0;
    width: 16px;
    height: 24px;
    white-space: nowrap;
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 16px;
    line-height: 24px;
    text-align: right;
  }
}

.card-content-section {
  position: absolute;
  top: 20px;
  left: 24px;
  width: 144px;
  height: 64px;
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  column-gap: 16px;

  .icon-container {
    flex-shrink: 0;
    width: 64px;
    height: 64px;
    position: relative;
    overflow: hidden;

    .star-bg-large {
      position: absolute;
      top: 0px;
      left: 4px;
      width: 56px;
      height: 64px;
      z-index: 1;
    }

    .star-bg-small {
      position: absolute;
      top: 8px;
      left: 12px;
      width: 40px;
      height: 48px;
      z-index: 2;
    }

    .main-icon {
      position: absolute;
      top: 20px;
      left: 20px;
      width: 24px;
      height: 24px;
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        filter: drop-shadow(0 0 2.4px rgba(19, 96, 134, 0.6));
      }
    }
  }

  .card-title {
    flex-shrink: 0;
    width: 64px;
    height: 24px;
    white-space: nowrap;
    color: #ffffff;
    font-family: 'Noto Sans SC', sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
  }
}
</style>
