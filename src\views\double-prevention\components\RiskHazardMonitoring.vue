<template>
  <div class="panel-container">
    <div class="flex items-center justify-between panel-header">
      <div class="header-title">入户类型分布</div>
      <div class="header-dropdown"></div>
    </div>
    <div class="p-4 panel-content">
      <div class="legend-row">
        <div class="custom-legend">
          <span class="legend-item">
            <span class="legend-color" style="background: #66ffff"></span>
            已整改
          </span>
          <span class="legend-item">
            <span class="legend-color" style="background: #409fff"></span>
            未整改
          </span>
        </div>
      </div>
      <div class="charts-row">
        <div v-for="(item, idx) in chartList" :key="item.title" class="single-chart">
          <div class="chart-content">
            <div :ref="el => (chartRefs[idx] = el as HTMLElement)" class="chart-container"></div>
          </div>
          <div class="chart-title">{{ item.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const chartList = [
  { title: '巡检检查', rectified: 3, unrectified: 1 },
  { title: '入户安检', rectified: 4, unrectified: 2 },
  { title: '随瓶安检', rectified: 12, unrectified: 3 },
  { title: '第三方施工', rectified: 17, unrectified: 5 },
  { title: '重点监控区域', rectified: 3, unrectified: 0 },
]

const chartRefs = ref<(HTMLElement | null)[]>([])
let chartInstances: echarts.ECharts[] = []

const getOption = (rectified: number, unrectified: number) => ({
  backgroundColor: 'transparent',
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)',
  },
  graphic: [
    {
      type: 'text',
      left: 'center',
      top: 'center',
      style: {
        text: `${rectified + unrectified}`,
        fill: '#fff',
        fontSize: 24,
        fontWeight: 700,
        fontFamily: 'DINPro, Noto Sans SC, Arial',
      },
      z: 10,
    },
  ],
  series: [
    {
      type: 'pie',
      radius: ['70%', '100%'],
      center: ['50%', '50%'],
      silent: true,
      z: 1,
      label: { show: false },
      labelLine: { show: false },
      data: [{ value: 1, itemStyle: { color: 'rgba(64, 159, 255, 0.3)' } }],
    },
    {
      name: '任务类型统计',
      type: 'pie',
      startAngle: 90,
      radius: ['80%', '92%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      z: 2,
      label: { show: false },
      emphasis: { show: false, scale: false },
      labelLine: { show: false },
      data: [
        {
          value: rectified,
          name: '已整改',
          itemStyle: {
            color: '#66FFFF',
          },
        },
        {
          value: unrectified,
          name: '未整改',
          itemStyle: {
            color: '#409FFF',
          },
        },
      ],
    },
  ],
})

const renderCharts = () => {
  chartInstances.forEach(c => c.dispose())
  chartInstances = []
  chartList.forEach((item, idx) => {
    const el = chartRefs.value[idx]
    if (el) {
      const chart = echarts.init(el)
      chart.setOption(getOption(item.rectified, item.unrectified))
      chartInstances.push(chart)
    }
  })
}

const handleResize = () => {
  chartInstances.forEach(c => c.resize())
}

onMounted(() => {
  nextTick(() => {
    renderCharts()
    window.addEventListener('resize', handleResize)
  })
})

onUnmounted(() => {
  chartInstances.forEach(c => c.dispose())
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
@import '@/styles/index.css';

.legend-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 8px;
  min-height: 32px;
}
.custom-legend {
  display: flex;
  gap: 24px;
  align-items: center;
  height: 32px;
}
.legend-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #fff;
  font-family: 'Noto Sans SC', Arial, sans-serif;
}
.legend-color {
  display: inline-block;
  width: 18px;
  height: 10px;
  border-radius: 2px;
  margin-right: 8px;
}
.charts-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  gap: 0;
}
.single-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120px;
}
.chart-content {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chart-title {
  color: #8ee6ff;
  font-size: 16px;
  margin-top: 8px;
  margin-bottom: 0;
  text-align: center;
  font-family: 'Noto Sans SC', Arial, sans-serif;
}
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
